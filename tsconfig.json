{"extends": "@edenx/tsconfig/base", "compilerOptions": {"declaration": false, "jsx": "preserve", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@shared/*": ["shared/*"], "@pages/*": ["src/pages/*"], "@components/*": ["src/components/*"], "@common/*": ["src/common/*"], "@stores/*": ["src/stores/*"], "@hooks/*": ["src/hooks/*"], "@sdks/*": ["src/sdks/*"], "@http_idl/*": ["src/http_idl/*"], "@utils/*": ["src/utils/*"]}, "types": ["react", "react-dom"]}, "include": ["src", "shared", "config", "edenx.config.ts"], "exclude": ["node_modules", "static", "./eden.pipeline.js", "./commitlint.config.js", "./build", "./output"]}