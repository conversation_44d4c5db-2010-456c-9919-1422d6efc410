/* eslint-disable */
// @ts-nocheck

import { axiosInstance } from '@common/http/index';

import * as base from './base';
export { base };

import { headers } from './_util';

export const ChannelType = {
  IM: 1,
  TICKET: 2,
  PHONE: 3,
  ADMIN: 4,
  ECOM_TICKET: 5,
  BUZZ: 6,
  FEEDBACK: 7,
  QUALITY_CHECK: 8,
  IM_OFFLINE_SESSION: 9,
  ACCESS_CALL: 10,
};

export const SkillLevel = {
  JUNIOR: 1,
  SENIOR: 2,
};

export class AgentSkillGroupService {
  constructor(uriPrefix) {
    this.uriPrefix = typeof uriPrefix !== 'undefined' ? uriPrefix : '';
  }

  GetAgentsByCondition(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/search/agents`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }
}

export const agentSkillGroupServiceClient = new AgentSkillGroupService();
