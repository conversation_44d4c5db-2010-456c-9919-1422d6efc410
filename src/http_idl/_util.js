/* eslint-disable */
// @ts-nocheck

export const headers = { 'Content-Type': 'application/json' };
export const method = 'GET';
export const credentials = 'same-origin';

export function queryStringify(params, inBody) {
  const keys = [];

  function itemStringify(obj, prefix) {
    const type = Object.prototype.toString.call(obj);
    if (type === '[object Array]') {
      obj.forEach((item, key) => {
        itemStringify(item, `${prefix}[${key}]`);
      });
    } else if (type === '[object Object]') {
      for (const key in obj) {
        itemStringify(obj[key], `${prefix}[${key}]`);
      }
    } else if (type === '[object Date]') {
      keys.push(`${prefix}=${obj.toISOString()}`);
    } else if (type === '[object Null]') {
      keys.push(`${prefix}=`);
    } else if (type !== '[object Undefined]') {
      keys.push(`${prefix}=${encodeURIComponent(obj)}`);
    }
  }

  for (const k in params) {
    itemStringify(params[k], k);
  }

  const str = keys.join('&');
  return str && !inBody ? `?${str}` : str;
}
