/* eslint-disable */
// @ts-nocheck

import { axiosInstance } from '@common/http/index';

import * as base from './base';
export { base };

import { method, headers, queryStringify } from './_util';

export const Scene = {
  SOP: 1,
  TOOL: 2,
};

export const Type = {
  TOOL: 1,
  ACTION: 2,
  QUERY: 3,
  INIT: 4,
};

export const TransType = {
  ToC: 0,
  ToB: 1,
};

export const PermissionPoint = {
  Development: 1,
  Product: 2,
  Operate: 3,
};

export const Target = {
  Api: 1,
  Info: 2,
};

export const OperateType = {
  CLOSE: 0,
  OPEN: 1,
  INCONFIG: 2,
};

export class InfoService {
  constructor(uriPrefix) {
    this.uriPrefix = typeof uriPrefix !== 'undefined' ? uriPrefix : '';
  }

  GetApi(request, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/getApi`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: request,
    });
  }

  GetOperateRecordList(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/getOperateRecordList`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetInsertOrUpdateSchema(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/getFormSchema`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  UpdateOrAddData(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/updateOrAddData`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  DisableOrEnableInfoOrApi(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/DisableOrEnableInfoOrApi`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetInfoSelectPageScheme(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetInfoSelectPageScheme`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetInfoDataList(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetInfoDataList`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  SaveInfo(request, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/saveInfo`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: request,
    });
  }

  SaveApi(request, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/saveApi`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: request,
    });
  }

  TransferInfo(request, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/transferInfo`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: request,
    });
  }

  SearchTransferUser(request, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/searchTransferUser`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: request,
    });
  }

  GetTransferTips(request, option) {
    const query = queryStringify(request);
    const uri = `${this.uriPrefix}/info_management/api/v1/getTransferTips${query}`;
    return axiosInstance({
      url: uri,
      method,
      headers,
    });
  }

  DeleteInfo(request, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/deleteEditInfo`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: request,
    });
  }

  DeleteApi(request, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/deleteEditApi`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: request,
    });
  }

  GetApiSelectPageScheme(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetApiSelectPageScheme`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetApiDataList(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetApiDataList`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetStarlingProjectInfo(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetProjectInfo`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetGqlLink(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetGqlLink`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetInfoTestResult(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetInfoTestResult`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }

  GetInfoTestData(req, option) {
    const uri = `${this.uriPrefix}/info_management/api/v1/GetInfoTestData`;
    return axiosInstance({
      url: uri,
      method: 'POST',
      headers,
      data: req,
    });
  }
}

export const infoServiceClient = new InfoService();
