/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import * as base from "./base";
export { base };

/** 操作渠道 */
export enum ChannelType {
  /** IM */
  IM = 1,
  /** 工单 */
  TICKET = 2,
  /** 电话 */
  PHONE = 3,
  /** 管理员 */
  ADMIN = 4,
  /** 电商工单 */
  ECOM_TICKET = 5,
  /** BUZZ工单 */
  BUZZ = 6,
  /** FEEDBACK */
  FEEDBACK = 7,
  /** 质检 */
  QUALITY_CHECK = 8,
  /** IM离线留言会话 */
  IM_OFFLINE_SESSION = 9,
  ACCESS_CALL = 10,
}

/** 触达外呼 */
export enum SkillLevel {
  /** 新手 */
  JUNIOR = 1,
  SENIOR = 2,
}

/** 老手 */
export interface Agent {
  ID: string;
  TenantId: string;
  WorkType: number;
  UserName: string;
  NickName: string;
  UserId: string;
  UUID: string;
  Email: string;
  Mobile: string;
  CompanyId: string;
  ChannelTypes: Array<ChannelType>;
  Status: number;
  CreatedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
  OperatorName: string;
  DepartmentId: string;
  ImMaxTaskNum: number;
  PhoneSeatNo?: string;
  /** 国家地区 */
  CountryRegion?: string;
  FeedbackMaxTaskNum?: number;
  NgccServiceLine?: string;
  QualityCheckMaxTaskNum?: number;
  /** 城市 */
  City?: string;
  /** 工区 */
  WorkArea?: string;
  /** 业务线 */
  BusinessLine?: string;
  /** 人员标签 */
  Tags?: { [key: string]: string };
  /** 人员技能等级 */
  SkillLevelMap?: { [key: number]: SkillLevel };
  Extra: { [key: string]: string };
}

/** 目前extra里包含的字段 c_n_agent_appid(IM appId), */
/** c_s_ecom_ticket_role(电商工单系统角色), */
/** c_n_ecom_ticket_is_super(电商工单是否超级管理员) */
/** c_s_ecom_ticket_kind(电商工单业务类型) */
export interface GetAgentsByConditionRequest {
  TenantId: string;
  Email?: string;
  UserName?: string;
  Status?: number;
  PageNo: number;
  PageSize: number;
  DepartmentId?: string;
  SkillGroupIds?: Array<string>;
  DepartmentIds?: Array<string>;
  ChannelType?: ChannelType;
  Base?: base.Base;
}

export interface GetAgentsByConditionResponse {
  Agents: Array<Agent>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export class AgentSkillGroupService {
  constructor(uriPrefix?: string);

  /** 根据条件，获取agent列表 */
  GetAgentsByCondition<T>(
    req: GetAgentsByConditionRequest,
    option?: T
  ): Promise<GetAgentsByConditionResponse>;
}

export const agentSkillGroupServiceClient: AgentSkillGroupService;
