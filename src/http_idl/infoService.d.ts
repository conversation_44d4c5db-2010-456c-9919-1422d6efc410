/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import * as base from "./base";
export { base };

export enum Scene {
  SOP = 1,
  TOOL = 2,
}

export enum Type {
  TOOL = 1,
  ACTION = 2,
  QUERY = 3,
  INIT = 4,
}

/** 表单字段中，哪些是需要做TOC翻译的字段，哪些是需要做TOB翻译的字段 */
export enum TransType {
  ToC = 0,
  ToB = 1,
}

export enum PermissionPoint {
  /** 研发 */
  Development = 1,
  /** 产品 */
  Product = 2,
  Operate = 3,
}

/** 坐席 */
/** 获取/操作资源对象 */
export enum Target {
  Api = 1,
  Info = 2,
}

/** 启用/禁用 */
export enum OperateType {
  /** 禁用 */
  CLOSE = 0,
  /** 启用 */
  OPEN = 1,
  INCONFIG = 2,
}

/** 配置中 */
export interface Info {
  /** id */
  id: string;
  /** api id */
  apiId: string;
  /** 带path，用于组装query+output标识 */
  key: string;
  /** 别名(一维,用于匹配api中的入参) */
  aliasKey: string;
  /** 展示名称 */
  name: string;
  /** 用户组件格式校验 string number boolean */
  type: string;
  /** 用户组件子类型 */
  subType?: string;
  /** 1是 0否 */
  isList: number;
  /** 名称全路径，用于搜索 */
  fullPath: string;
  /** JSON 渲染字面需要，也包含支持的操作符 */
  fieldOption: string;
  desc: string;
}

/** info描述,用于辅助运营理解 */
export interface Api {
  id?: string;
  apiKey?: string;
  name?: string;
  type?: number;
  target?: string;
  func?: string;
  initParams?: string;
  initFormat?: string;
  queryPrefix?: string;
  scene?: string;
  apiDesc?: string;
}

export interface GetApiRequest {
  /** 场景 */
  scene: Scene;
  /** 租户ID，字节为1 */
  tenantId: string;
  /** 接入方ID，可选，与新工单接入方定义相同 */
  accessPartyId?: number;
  /** 类型 */
  type?: Type;
  /** 模糊搜索  名称 */
  potentialName?: string;
  Base?: base.Base;
}

export interface GetApiResponse {
  /** api列表 */
  apiList?: Array<Api>;
  BaseResp: base.BaseResp;
}

export interface GetInfoSelectPageSchemeRequest {
  permissionPoint?: PermissionPoint;
}

/** 权限点 */
export interface GetInfoSelectPageSchemeResponse {
  /** Info筛选项scheme */
  selectScheme: string;
  /** Info列表页scheme */
  pageScheme: string;
  BaseResp: base.BaseResp;
}

export interface GetInfoDataListRequest {
  /** 筛选项 */
  selectMap: { [key: string]: string };
  /** 权限点 */
  permissionPoint?: PermissionPoint;
  /** 页数 */
  pageNum: number;
  pageSize: number;
}

/** 页数大小 */
export interface DescOptions {
  contentTitle?: string;
  contentDesc?: string;
  data: Array<Array<string>>;
}

export interface Data {
  key: string;
  value: string;
  type: string;
  desc?: string;
  descOptions?: DescOptions;
  options?: Array<OptionItem>;
  color?: string;
}

export interface OptionItem {
  title: string;
  itemList: Array<string>;
}

export interface GetInfoDataListResponse {
  /** 列表数据 */
  infoDataList: Array<Array<Data>>;
  /** info数据总量 */
  total: string;
  BaseResp: base.BaseResp;
}

export interface GetApiSelectPageSchemeRequest {
  permissionPoint?: PermissionPoint;
}

/** 权限点 */
export interface GetApiSelectPageSchemeResponse {
  /** Api筛选项scheme */
  selectScheme: string;
  /** Api列表页scheme */
  pageScheme: string;
  BaseResp: base.BaseResp;
}

export interface GetApiDataListRequest {
  /** 筛选项 */
  selectMap: { [key: string]: string };
  /** 权限点 */
  permissionPoint?: PermissionPoint;
  /** 页数 */
  pageNum: number;
  pageSize: number;
}

/** 页数大小 */
export interface GetApiDataListResponse {
  /** 列表数据 */
  apiDataList: Array<Array<Data>>;
  /** api数据总量 */
  total: string;
  BaseResp: base.BaseResp;
}

export interface DisableOrEnableInfoOrApiRequest {
  /** 操作对象 */
  target: Target;
  /** 操作对象id */
  id: string;
  /** 操作类型 */
  operateType: OperateType;
  /** 操作人 */
  operateUserName: string;
  /** 权限点 */
  permissionPoint?: PermissionPoint;
  Base?: base.Base;
}

export interface DisableOrEnableInfoOrApiResponse {
  /** 操作结果 */
  result: boolean;
  /** 操作结果信息 */
  message: string;
  BaseResp: base.BaseResp;
}

export interface GetInsertOrUpdateSchemaRequest {
  permissionPoint: PermissionPoint;
  target: Target;
  dataId?: string;
  Base?: base.Base;
}

export interface GetInsertOrUpdateSchemaResponse {
  schema?: string;
  starlingMap?: { [key: string]: string };
  status?: number;
  BaseResp: base.BaseResp;
}

export interface GetOperateRecordListRequest {
  /** 查询目标 */
  target: Target;
  /** 查询目标Id */
  id: string;
  /** 查询时间start */
  stratTime?: string;
  /** 查询时间end */
  endTime?: string;
  /** 页面大小不传默认15 */
  pageSize?: number;
  /** 页码，不传默认为1 */
  pageNum?: number;
  /** 当前查询时间 */
  queryTime: string;
  Base?: base.Base;
}

export interface OperateRecord {
  /** id */
  id?: string;
  /** 操作内容 */
  operateDetail?: Array<string>;
  /** 操作时间 */
  operateTime?: string;
  /** 操作人 */
  operateUserMail?: string;
  /** 操作人姓名 */
  operateUserName?: string;
  operateDesc?: string;
}

export interface GetOperateRecordListResponse {
  /** 操作记录 */
  operateRecordList?: Array<OperateRecord>;
  /** 总数 */
  totalCount?: number;
  /** 总页数 */
  pageCount?: number;
  BaseResp: base.BaseResp;
}

export interface CheckFailedRecord {
  /** 校验错误字段 */
  failedKey?: string;
  failedDesc?: string;
}

/** 校验错误原因 */
/** newData参数 */
/** 公用 */
/** private String id; */
/** private String name; */
/** private Integer category;      // 1: api  2: info  用于区分data类型，前端不用传 */
/** private String type; */
/** private String operateUser;    //非schema字段      前端不用传 */
/** private Long tenantId; */
/** private List<String> accessPartyIds; */
/** api： */
/** private String apiKey; */
/** private String scene; */
/** private String target; */
/** private String func; */
/** private String initParams; */
/** private String initFormat; */
/** private String queryPrefix; */
/** private String apiDesc; */
/** info： */
/** private String apiId; */
/** private String infoKey; */
/** private String aliasKey; */
/** private String instruction; */
/** private String subType; */
/** private Boolean isList; */
/** private String enumType; */
/** private List<Map<String, String>> enumMap;    // 提交的枚举列表 */
/** private String fieldOptionQuery; */
/** private String fieldOptionVariable; */
/** private String fieldOptionJsonPath; */
/** private String optionType; */
/** private String infoDesc; */
/** private String fullPath; */
/** private String testData; */
/** private List<String> applications; */
/** private String source; */
export interface UpdateOrAddDataRequest {
  /** 目标 */
  target: Target;
  /** 新数据 */
  newData: string;
  /** 操作人邮箱 */
  operateUserMail: string;
  /** dataId */
  dataId?: string;
  permissionPoint: PermissionPoint;
  Base?: base.Base;
}

export interface UpdateOrAddDataResponse {
  failRecordList: Array<CheckFailedRecord>;
  BaseResp: base.BaseResp;
}

/** ================================================================= INFO流程线上化 START ================================================================= */
export interface SaveInfoRequest {
  /** 数据 */
  data: string;
  /** 操作人邮箱,操作人姓名 */
  operateUserMail: string;
  /** infoId 没有Id代表新建 */
  infoId?: string;
  /** 权限 */
  permissionPoint: PermissionPoint;
  Base?: base.Base;
}

export interface SaveInfoResponse {
  infoId: string;
  BaseResp: base.BaseResp;
}

export interface SaveApiRequest {
  /** 数据 */
  data: string;
  /** 操作人邮箱 */
  operateUserMail: string;
  /** apiId 没有Id代表新建 */
  apiId?: string;
  /** 权限 */
  permissionPoint: PermissionPoint;
  Base?: base.Base;
}

export interface SaveApiResponse {
  apiId: string;
  BaseResp: base.BaseResp;
}

export interface TransferInfoRequest {
  /** infoId 没有Id代表新建 */
  infoId: string;
  /** 操作流转邮箱,姓名 */
  operateTransferUserMail: string;
  /** 流转人邮箱，姓名 */
  transferToUserMail: string;
  Base?: base.Base;
}

export interface TransferInfoResponse {
  BaseResp: base.BaseResp;
}

export interface SearchTransferUserRequest {
  /** 搜索内容 */
  searchContent: string;
  Base?: base.Base;
}

export interface TransferUser {
  email: string;
  name: string;
}

export interface SearchTransferUserResponse {
  /** 搜索流转人列表，格式：邮箱,操作人姓名 */
  transferUserList: Array<TransferUser>;
  BaseResp: base.BaseResp;
}

export interface DeleteInfoRequest {
  /** infoId */
  infoId?: string;
  /** 操作流转邮箱,操作人姓名 */
  operateUserMail: string;
  Base?: base.Base;
}

export interface DeleteInfoResponse {
  BaseResp: base.BaseResp;
}

export interface DeleteApiRequest {
  /** apiId */
  apiId?: string;
  /** 操作流转邮箱,操作人姓名 */
  operateUserMail: string;
  Base?: base.Base;
}

export interface DeleteApiResponse {
  BaseResp: base.BaseResp;
}

export interface GetTransferTipsRequest {
  Base?: base.Base;
}

export interface GetTransferTipsResponse {
  transferTips: string;
  BaseResp: base.BaseResp;
}

export interface GetStarlingProjectInfoRequest {
  accessPartyId: string;
  Base?: base.Base;
}

export interface GetStarlingProjectInfoResponse {
  starlingProjectInfo?: { [key: string]: string };
}

/** ================================================================= INFO流程线上化 END   ================================================================= */
/** ================================================================= INFO 二期优化 START ================================================================= */
export interface GetGqlLinkRequest {
  /** 测试数据 */
  newData: string;
  Base?: base.Base;
}

export interface GetGqlLinkResponse {
  /** Gql链接 */
  gqlLink?: string;
  BaseResp: base.BaseResp;
}

/** 请求传入全部表单数据，按提交接口的格式 */
export interface GetInfoTestResultRequest {
  /** 测试数据 */
  newData: string;
  Base?: base.Base;
}

export interface GetInfoTestResultResponse {
  success?: boolean;
  /** info测试数据结果 */
  infoTestResult?: string;
  /** 失败原因 */
  failReason?: Array<string>;
  BaseResp: base.BaseResp;
}

export interface GetInfoTestDataRequest {
  /** api ID必传 */
  newData?: string;
  /** 操作人邮箱 */
  operateUserMail: string;
  Base?: base.Base;
}

export interface GetInfoTestDataResponse {
  /** 测试数据map */
  infoTestData?: { [key: string]: string };
  /** 测试数据是否获取成功 */
  infoTestDataSuccess?: boolean;
  /** 测试数据未获取完整提示信息 */
  infoTestDataTip?: string;
  BaseResp: base.BaseResp;
}

/** ================================================================= INFO 二期优化 END ================================================================= */
export class InfoService {
  constructor(uriPrefix?: string);

  GetApi<T>(request: GetApiRequest, option?: T): Promise<GetApiResponse>;

  /** 获取操作记录 */
  GetOperateRecordList<T>(
    req: GetOperateRecordListRequest,
    option?: T
  ): Promise<GetOperateRecordListResponse>;

  /** 获取API/INFO表单schema和表单数据 */
  GetInsertOrUpdateSchema<T>(
    req: GetInsertOrUpdateSchemaRequest,
    option?: T
  ): Promise<GetInsertOrUpdateSchemaResponse>;

  /** Info/API新增/修改接口 */
  UpdateOrAddData<T>(
    req: UpdateOrAddDataRequest,
    option?: T
  ): Promise<UpdateOrAddDataResponse>;

  /** 禁用/启用Info/API */
  DisableOrEnableInfoOrApi<T>(
    req: DisableOrEnableInfoOrApiRequest,
    option?: T
  ): Promise<DisableOrEnableInfoOrApiResponse>;

  /** 获取Info列表/筛选项Scheme */
  GetInfoSelectPageScheme<T>(
    req: GetInfoSelectPageSchemeRequest,
    option?: T
  ): Promise<GetInfoSelectPageSchemeResponse>;

  /** 获取Info列表页数据 */
  GetInfoDataList<T>(
    req: GetInfoDataListRequest,
    option?: T
  ): Promise<GetInfoDataListResponse>;

  /** 保存INFO */
  SaveInfo<T>(request: SaveInfoRequest, option?: T): Promise<SaveInfoResponse>;

  /** 保存API */
  SaveApi<T>(request: SaveApiRequest, option?: T): Promise<SaveApiResponse>;

  /** 流转INFO */
  TransferInfo<T>(
    request: TransferInfoRequest,
    option?: T
  ): Promise<TransferInfoResponse>;

  /** 搜索流转人 */
  SearchTransferUser<T>(
    request: SearchTransferUserRequest,
    option?: T
  ): Promise<SearchTransferUserResponse>;

  /** 获取流转提示文字 */
  GetTransferTips<T>(
    request: GetTransferTipsRequest,
    option?: T
  ): Promise<GetTransferTipsResponse>;

  /** 删除编辑态INFO */
  DeleteInfo<T>(
    request: DeleteInfoRequest,
    option?: T
  ): Promise<DeleteInfoResponse>;

  /** 删除编辑态API */
  DeleteApi<T>(
    request: DeleteApiRequest,
    option?: T
  ): Promise<DeleteApiResponse>;

  /** 获取API列表/筛选项Scheme */
  GetApiSelectPageScheme<T>(
    req: GetApiSelectPageSchemeRequest,
    option?: T
  ): Promise<GetApiSelectPageSchemeResponse>;

  /** 获取API列表页数据 */
  GetApiDataList<T>(
    req: GetApiDataListRequest,
    option?: T
  ): Promise<GetApiDataListResponse>;

  /** 获取starling项目数据 */
  GetStarlingProjectInfo<T>(
    req: GetStarlingProjectInfoRequest,
    option?: T
  ): Promise<GetStarlingProjectInfoResponse>;

  /** 获取INFO GQL链接 */
  GetGqlLink<T>(
    req: GetGqlLinkRequest,
    option?: T
  ): Promise<GetGqlLinkResponse>;

  /** info配置页面展示运行结果 */
  GetInfoTestResult<T>(
    req: GetInfoTestResultRequest,
    option?: T
  ): Promise<GetInfoTestResultResponse>;

  /** info配置页面获取测试数据 */
  GetInfoTestData<T>(
    req: GetInfoTestDataRequest,
    option?: T
  ): Promise<GetInfoTestDataResponse>;
}

export const infoServiceClient: InfoService;
