import { ReqExecNodeStatus, ArgosLogLevel } from '@/common/constants/info';

export interface ExecResult {
  status: 'success' | 'fail';
  errorMsg: string[];
  logId: string;
  data: string;
  rawResp: string;
}

export interface InfoDetail {
  infoKey: string;
  infoName: string;
  infoDesc: string;
  infoID: string;
  apiID: string;
}

export interface ArgosLog {
  logMsg: string;
  pod: string;
  ip: string;
  spanId: string;
  timestamp: string;
  idc: string;
  psm: string;
  logId: string;
  location: string;
  level: ArgosLogLevel;
  ifPpe: boolean;
}

export interface ExecutionProgressValue {
  status: ReqExecNodeStatus;
  errorDetails: string[];
  logs: ArgosLog[];
}

// export interface LogData {
//   errorList: Record<ReqExecNode, ExecutionProgressValue>;
//   queryResults: {
//     requestId: string;
//     timestamp: string;
//     requestParams: Record<string, string>;
//     requestInfokeys: string[];
//     infoKeyResults: {
//       infoKey: string;
//       infoValue: string;
//       valueSource: number;
//     }[];
//   }[];
//   logFindSuccess: boolean;
// }
