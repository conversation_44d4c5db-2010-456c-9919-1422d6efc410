import {
  CHANNEL_FILTER,
  platform,
  PRODUCT_LINE_CODE,
} from '@common/constants/index';
import { shared } from '@ies/united-sdk-i18n';

export function getQueryChannel() {
  const searchQuery = new URLSearchParams(window.location.search);
  return searchQuery.get('channel') ?? '';
}

export function getDefaultChannel() {
  const { extra, productLine } = shared.getCoreData();

  if (
    extra?.platform === platform.agentOperation &&
    productLine === PRODUCT_LINE_CODE.l3
  ) {
    return `${CHANNEL_FILTER.L3Judgement},${CHANNEL_FILTER.L3Text}`;
  }

  return '';
}
