/* eslint-disable @typescript-eslint/naming-convention */

export const isBoe =
  window?.$unitedHelpdeskI18nInitData?.currentRegion === 'BOEI18N';

export const isProd =
  window?.$unitedHelpdeskI18nInitData?.currentRegion !== 'BOEI18N';

export const isSg = (): boolean =>
  window?.$unitedHelpdeskI18nInitData?.currentRegion === 'SG';
export const isVa = (): boolean =>
  window?.$unitedHelpdeskI18nInitData?.currentRegion === 'VA';
export const isBoeI18n = (): boolean =>
  window?.$unitedHelpdeskI18nInitData?.currentRegion === 'BOEI18N';
export const isTTP = (): boolean =>
  window?.$unitedHelpdeskI18nInitData?.currentRegion === 'TTP';

// eslint-disable-next-line arrow-body-style
export const isOverseas = (): boolean => {
  return [
    'csp-sg.byteoversea.net',
    'csp-bos.byteoversea.net',
    'csp-sg.bytelemon.com',
    'csp-gcp.bytelemon.com',
    'csp-gcp.byteintl.net',
    'csp-va.byteoversea.net',
    'csp-va.bytelemon.com',
    'csp-gcp.bytelemon.com',
    'csp-gcp.byteintl.net',
    'csp-migrate.byteintl.net',
    'csp-tx.tiktokd.net',
    'csp.tiktok-usts.net',
    'csp.tiktokv-us.com',
    'csp.byteintl.net',
    'csp.byteintl.com',
    'csp.tiktok-row.net',
  ].includes(window.location.host);
};

export enum Region {
  sg = 'sg',
  va = 'va',
  ttp = 'ttp',
  cn = 'cn',
  boe = 'boe',
  boei18n = 'boei18n',
}

let region = Region.sg;
if (isBoeI18n()) {
  region = Region.boei18n;
} else if (isSg()) {
  region = Region.sg;
} else if (isVa()) {
  region = Region.va;
} else if (isTTP()) {
  region = Region.ttp;
}
export const currentRegion: Region = region;
