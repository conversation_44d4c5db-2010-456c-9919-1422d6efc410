import * as React from 'react';
import { Route, Switch } from '@edenx/runtime/router-v5';
import loadableComp from '@components/global/loading';
import RouteRender from '@components/global/RouteRender';

const { lazy } = React;

const APIList = loadableComp(lazy(() => import('@pages/API/List')));
const INFOList = loadableComp(lazy(() => import('@pages/INFO/List')));
const INFODebug = loadableComp(lazy(() => import('@pages/INFO/debug')));
const APIDetail = loadableComp(lazy(() => import('@pages/API/Detail')));
const INFODetail = loadableComp(lazy(() => import('@pages/INFO/Detail')));
const NoMatch = loadableComp(lazy(() => import('@pages/no_match')));

const routeConfig = [
  {
    path: '/info/list',
    component: INFOList,
  },
  {
    path: '/info/detail',
    component: INFODetail,
  },
  {
    path: '/info/debug',
    component: INFODebug,
  },
  {
    path: '/api/list',
    component: APIList,
  },
  {
    path: '/api/detail',
    component: APIDetail,
  },
];

export default function AppRouter(): React.FunctionComponentElement<React.ReactNode> {
  return (
    <Switch>
      <RouteRender routeConfig={routeConfig} />
      <Route component={NoMatch} />
    </Switch>
  );
}
