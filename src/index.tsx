import { createRoot, Root } from 'react-dom/client';
import { fetchLocaleDatas } from './sdks/i18n';
import { StoreProvider } from './stores';
import { PATH_PREFIX } from './const';
import { slardarInit } from '@sdks/slardar';
import './index.css';
declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    __PROWER_BY_GAR__: boolean;
    $unitedHelpdeskI18nInitData?: any;
    gfdatav1?: {
      version: string;
    };
  }
}
const init = async (
  dom: Element | null,
  basename: string,
): Promise<Root | null> => {
  await fetchLocaleDatas();
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const App = require('./App.tsx').default; // 这里需要改成动态加载，否则常量无法翻译
  slardarInit();
  const container = dom
    ? dom.querySelector('#root')
    : document.querySelector('#root');
  let root = null;
  if (container) {
    root = createRoot(container);
    root.render(
      <StoreProvider>
        <App basename={basename} name="info" />
      </StoreProvider>,
    );
  }
  return root;
};

export function provider(): any {
  let root: Root | null = null;
  return {
    async render({
      dom,
      basename,
    }: {
      dom: Element;
      basename: string;
    }): Promise<void> {
      root = await init(dom, basename);
    },
    destroy(): void {
      if (root) {
        root.unmount();
        root = null;
      }
    },
  };
}

if (!window.__PROWER_BY_GAR__) {
  init(null, PATH_PREFIX);
}
