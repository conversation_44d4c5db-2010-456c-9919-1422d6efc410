import axios, { AxiosInstance, AxiosResponse } from 'axios';

export const SYSTEM_ERROR_CODE = -1;
export const SYSTEM_ERROR_MESSAGE = 'Network Error';

export const GATEWAY_PATH = '/gateway/request/transfer';

// Error codes
export const INVALID_RESPONSE_CODE = -1;
export const GATEWAY_ERROR_CODE = -2;
export const NETWORK_ERROR_CODE = 0;
export const INVALID_RESPONSE_MESSAGE = 'Invalid response body';
export const GATEWAY_ERROR_MESSAGE = 'Gateway Error';
export const NETWORK_ERROR_MESSAGE = 'Network Error';

// Gateway response status constants
export const GATEWAY_SUCCESS_STATUS = '0';
export const GATEWAY_SUCCESS_MESSAGE = 'success';

// Interface for error response
export interface ErrorResponse {
  statusCode: number;
  statusMsg: string;
  error: string;
  logId?: string;
}

// Type guard to check if an error is an ErrorResponse
export function isErrorResponse(error: unknown): error is ErrorResponse {
  return (
    typeof error === 'object' &&
    error !== null &&
    'statusCode' in error &&
    'statusMsg' in error &&
    'error' in error
  );
}

// Interface for system error response
export interface SystemErrorResponse {
  statusCode: number;
  statusMsg: string;
  ext: {
    rawError: unknown;
  };
}

/**
 * Helper function to handle gateway responses
 */
function handleGatewayResponse<T>(
  response: AxiosResponse,
  logId: string,
): Promise<T & { logId: string }> | ErrorResponse | Promise<never> {
  if (typeof response?.data !== 'object' || response?.data === null) {
    const errorObj: ErrorResponse = {
      statusCode: INVALID_RESPONSE_CODE,
      statusMsg: INVALID_RESPONSE_MESSAGE,
      error: String(response?.data),
      logId,
    };
    return Promise.reject(errorObj);
  }

  const responseData = response?.data;

  if (
    responseData?.status !== GATEWAY_SUCCESS_STATUS ||
    responseData?.message !== GATEWAY_SUCCESS_MESSAGE
  ) {
    const errorObj: ErrorResponse = {
      statusCode: GATEWAY_ERROR_CODE,
      statusMsg: GATEWAY_ERROR_MESSAGE,
      error: responseData?.message || '',
      logId,
    };
    return Promise.reject(errorObj);
  }

  const result = responseData?.data as T;

  // Return the result with logId added
  return Promise.resolve({
    ...result,
    logId,
  });
}

/**
 * Helper function to handle gateway network errors
 */
function handleGatewayNetworkError(err: unknown): ErrorResponse {
  let errorMessage = 'Unknown error';

  // Try to extract status and statusText from axios error
  if (axios.isAxiosError(err) && err.response?.status) {
    errorMessage = `${err.response?.status} ${err.response?.statusText || ''}`;
  } else if (err instanceof Error && err?.message) {
    errorMessage = err.message;
  }

  // Extract log ID if available (might be undefined for network errors)
  let logId: string | undefined;
  if (axios.isAxiosError(err) && err.response?.headers?.['x-tt-logid']) {
    logId = err.response?.headers?.['x-tt-logid'] as string;
  }

  return {
    statusCode: NETWORK_ERROR_CODE,
    statusMsg: NETWORK_ERROR_MESSAGE,
    error: errorMessage,
    logId,
  };
}

// Create a configured axios instance
export const axiosInstance: AxiosInstance = axios.create();

// Request interceptor
axiosInstance.interceptors.request.use(
  config => config,
  error => Promise.reject(error),
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    const isGatewayRequest = response.config.url?.includes(GATEWAY_PATH);

    if (isGatewayRequest) {
      // Extract log ID from header
      const logId = (response?.headers?.['x-tt-logid'] as string) || '';
      return handleGatewayResponse(response, logId);
    }

    // For non-gateway requests, return response data
    return response.data;
  },
  err => {
    // If this is an error from our validation, pass it through
    if (isErrorResponse(err)) {
      return Promise.reject(err);
    }

    // Check if this is a gateway request
    const isGatewayRequest = err.config?.url?.includes(GATEWAY_PATH);

    if (isGatewayRequest) {
      // For gateway requests, create a network error response
      const errorResponse = handleGatewayNetworkError(err);
      return Promise.reject(errorResponse);
    }

    // For non-gateway requests, handle errors as before
    const errorResponse: SystemErrorResponse = {
      statusCode: SYSTEM_ERROR_CODE,
      statusMsg: SYSTEM_ERROR_MESSAGE,
      ext: {
        rawError: err,
      },
    };
    return Promise.resolve(errorResponse);
  },
);
