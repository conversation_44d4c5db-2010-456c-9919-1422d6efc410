/* eslint-disable unicorn/filename-case */
import InfoServiceRpcService from '@/bam-auto-generate/infoServiceRPC';
import { transferRPC } from './transfer';

export const infoServiceRpcService = new InfoServiceRpcService({
  request: config => {
    const { rpc, data } = config;
    return transferRPC({
      psm: 'ies.kefu.info_service',
      method: rpc,
      requestParams: data,
    });
  },
});
