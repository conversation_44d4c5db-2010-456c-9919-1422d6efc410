import { format } from 'date-fns';
import { isNil } from 'lodash-es';

/**
 * 功能：将时间戳数组转换成 时间字符串，通过～组合在一起
 * @param values
 * @returns string
 */
export const formatDataListToString = (values: Array<Date>): string => {
  try {
    const formatValues = values.map(item =>
      format(item, 'yyyy-MM-dd HH:mm:ss'),
    );
    return formatValues.join('~');
  } catch (err) {
    return '';
  }
};

/**
 * 功能：将对象中所有value为null或undefinedkey，全部置为空字符串
 * @param valueInfo [key: string]: any
 * @return result [key: string]: Array | string
 */
interface IvalueInfo {
  [key: string]: any;
}
export const formatNilValueToString = (valueInfo: IvalueInfo): IvalueInfo => {
  for (const key in valueInfo) {
    if (isNil(valueInfo[key])) {
      valueInfo[key] = '';
    }
  }
  return valueInfo;
};
