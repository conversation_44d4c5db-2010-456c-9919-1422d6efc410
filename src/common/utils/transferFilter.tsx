/* eslint-disable @typescript-eslint/naming-convention */
// 从接口处获取的筛选项的shcema
import { ReactNode } from 'react';
import { ResFilterProps } from '@common/constants/listConfig';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { agentSkillGroupServiceClient } from '@http_idl/agent';
import { Filter } from '@hi-design/ui';

import {
  formatSchemaWithStarlingKey,
  ManageType,
  SchemaType,
} from './starlingKey';
import { getDefaultChannel } from '@utils/url';

// 定义人员搜索方法
const searchPeople = async (keyword: string): Promise<Array<any>> => {
  try {
    const peopleList = await agentSkillGroupServiceClient.GetAgentsByCondition({
      TenantId: '1',
      UserName: keyword,
      PageNo: 1,
      PageSize: 50,
    });
    if (peopleList?.BaseResp?.StatusCode === 0) {
      const list = peopleList.Agents?.map(item => ({
        name: item.UserName,
        email: item.Email,
        uuid: item.UUID,
      }));
      return list || [];
    }
  } catch (err) {
    console.log('searchPeople err:', err);
  }
  return [];
};

// 为每种类型指定组件
const compType = {
  MultiSelect: (item: ResFilterProps): ReactNode => (
    <Filter.StaticSelect
      placeholder={COMMON_FE_TEXT_MAP.Placeholder_Search_Select}
      key={item.key}
      field={item.key}
      label={item.label}
      optionList={item.props?.options || []}
      defaultValue={item.props?.defaultValue ?? []}
    />
  ),
  InputText: (item: ResFilterProps): ReactNode => (
    <Filter.Input
      key={item.key}
      field={item.key}
      label={item.label}
      placeholder={COMMON_FE_TEXT_MAP.Placeholder_Enter_Text}
    />
  ),
  PeopleSearch: (item: ResFilterProps): ReactNode => (
    <Filter.PeopleSelect
      placeholder={COMMON_FE_TEXT_MAP.Placeholder_Search_People}
      filter
      remote
      key={item.key}
      field={item.key}
      label={item.label}
      defaultValue={[]}
      onSearch={searchPeople}
    />
  ),
  TimeRange: (item: ResFilterProps): ReactNode => (
    <Filter.DateRange
      key={item.key}
      field={item.key}
      label={item.label}
      placeholder={COMMON_FE_TEXT_MAP.Placeholder_Search_Time}
    />
  ),
};

/**
 * @description: 将从后端获取的筛选项的shcema转换格式
 * @param {string} schema: 从后端获取的筛选项的shcema
 * @param {ManageType} manageType 用于判断是INFO的选项schema还是API的选项schema，用于获取多语言starling Key
 * @return {*}
 */
const transferFilter = (
  schema: string,
  manageType: ManageType,
): Array<ReactNode> => {
  try {
    // 通过类型判断筛选项对应的组件
    const findByType = (item: ResFilterProps): ReactNode => {
      if (!item.type || !item.label || !item.key) {
        return;
      }
      const comp = compType[item.type](item);
      return comp ? comp : <div />;
    };
    const listItems = [];
    const schemaData = formatSchemaWithStarlingKey({
      schema,
      schemaType: SchemaType.SELECT,
      manageType,
    });
    schemaData?.forEach(item => {
      if (item.key === 'applications') {
        item.props.defaultValue = getDefaultChannel().split(',');
      }
      const comp = findByType(item);
      comp && listItems.push(comp);
    });
    return listItems;
  } catch (err) {
    console.log('transferFilter err:', err);
    return [];
  }
};

export default transferFilter;
