import { FormFieldMap } from '@common/constants/formSchema';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { getFieldType, getValuesStarling } from '@common/utils/starlingKey';
import {
  GetInsertOrUpdateSchemaResponse,
  infoServiceClient,
} from '@http_idl/infoService';
import { cspTranslateGen } from '@ies/csp-translate-gen-semi2';
import { shared } from '@ies/united-sdk-i18n';
import { isOverseas } from '@utils/env';
import { StarlingMapType } from '@hooks/useWindowFormState';
import { isEmpty } from 'lodash-es';
import { MutableRefObject } from 'react';

interface CheckOverseasTranslationType {
  fieldMap: FormFieldMap; // 用于海外记录toc、tob字段类型
  value: any; // 表单数据
  starlingMapRef: MutableRefObject<StarlingMapType>; //  用于存储该条INFO的starling数据，包括项目ID，空间ID，空间名称，apiKey
  setToastError: (message: string) => void;
}
// 仅在海外翻译使用，发布时校验翻译是否全量发布
export const checkOverseasTranslation = async (
  props: CheckOverseasTranslationType,
) => {
  if (!isOverseas()) {
    return true;
  }
  const { fieldMap, value, starlingMapRef, setToastError } = props || {};
  const { isHaveAgent, isHaveUser } = getFieldType(fieldMap);
  // 校验当前文案是否已经翻译完成，如果没有进行翻译或翻译未完成，不允许发布
  const userSchema = getValuesStarling(
    fieldMap.toC,
    value,
    starlingMapRef?.current?.namespaceIdToC,
  )?.schema;
  const agentSchema = getValuesStarling(
    fieldMap.toB,
    value,
    starlingMapRef?.current?.namespaceIdToB,
  )?.schema;
  let isHaveNotFullPushText = true;
  const checkDiffConfig = {} as any;
  try {
    if (isHaveAgent) {
      checkDiffConfig.agent = {
        jsonSchema: agentSchema,
      };
    }
    if (isHaveUser) {
      checkDiffConfig.user = {
        jsonSchema: userSchema,
      };
    }
    if (!isEmpty(checkDiffConfig)) {
      isHaveNotFullPushText =
        await cspTranslateGen.publishCheckDiff(checkDiffConfig);
    }
  } catch (e) {
    setToastError?.(COMMON_FE_TEXT_MAP.Trans_Check_Fail);
  }
  return isHaveNotFullPushText;
};

// 仅在海外使用，获取starling信息
interface GetStarlingProjectInfoType {
  records: GetInsertOrUpdateSchemaResponse;
  starlingMapRef: MutableRefObject<StarlingMapType>;
  setInitStarlingMap: (starlingInfoMap: any) => void;
}
export const getStarlingProjectInfo = async (
  params: GetStarlingProjectInfoType,
) => {
  try {
    if (!isOverseas()) {
      return;
    }
    const { records, starlingMapRef, setInitStarlingMap } = params || {};
    const accessPartyId = shared.getAccessPartyId();
    const starlingProjectInfo =
      (await infoServiceClient.GetStarlingProjectInfo({
        accessPartyId,
      })) || {};
    const userLangList = records?.starlingMap?.userLangList
      ? {
          userLangList: JSON.parse(records?.starlingMap?.userLangList),
        }
      : {};
    const starlingInfoMap = {
      ...records?.starlingMap,
      ...starlingProjectInfo,
      ...userLangList,
    };
    starlingMapRef.current = starlingInfoMap;
    setInitStarlingMap?.(starlingInfoMap);
  } catch (e) {
    console.error('getStarlingProjectInfo error', e);
  }
};
