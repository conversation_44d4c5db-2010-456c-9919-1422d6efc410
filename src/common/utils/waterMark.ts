import { I18n } from '@ies/starling_intl';
import watermark from '@byted/ssa_watermark';

let hasWatermark = false;

function initWatemark(text): void {
  if (!hasWatermark) {
    watermark({
      text,
      // 水印字体
      fontSize: 16,
      // 水印透明度
      opacity: navigator.platform === 'MacIntel' ? 0.1 : 0.12,
      // 水印颜色
      color: 'rgb(156,162,169)',
      // 水印图片大小控制
      // width = 150,
      // height = 150,
      // 修改水印回调
      onChange: () => {
        alert(
          I18n.t(
            'do_not_modify_the_watermark_your_operation_has_been_recorded',
            {},
            '请勿修改水印，你的操作已被记录',
          ),
        );
      },
      ignoreAttr: [],
      // 暗水印配置，详细参考Feature v1.5.0 说明
      hiddenMarkOpt: {
        // 是否需要暗水印，默认 false，则不会生成暗水印 class
        needHiddenMark: true,
        // 是否只需要暗水印，默认 false，为 true 则不会渲染全dom 覆盖的明水印
        justHiddenMark: false,
        // 暗水印背景图请求 api
        api: '/watermark/api/gen',
        // 暗水印class，被赋予该class的 dom 会使用 background image形式覆盖暗水印图片
        className: 'united-helpdesk-tag-wm',
        // 生成背景图颜色，默认rgb(255,255,255)
        bgColor: 'rgb(255,255,255)',
      },
    });
    hasWatermark = true;
  }
}

export default initWatemark;
