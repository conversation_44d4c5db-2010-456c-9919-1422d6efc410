/**
 * 功能：将后端返回的schema转换成表单库可用的表单格式
 * @param schema 后端返回表单数据
 * @param isEdit 是否是编辑页，用于控制生成表单的disabled状态
 *
 * @return schema 表单库可用的schema
 * @return value 各个字段的value对象集合
 * @return effectValueList 清空字段数据集合
 *
 * @function formatSchemaItem 用于针对表单字段的每一项进行解析，注入需要特殊处理的字段值，并且进行错误兜底处理
 * 若该条字段解析错误，则展示input类型，令用户可以正常提交
 */
import {
  ArrayContentRender,
  ArrayItemsRender,
  ArrayRender,
} from '@components/CustomerFormRender/CustomerArrayFieldItem';
import CustomerFormApiAccess from '@components/CustomerFormRender/CustomerFromApiAccess';
import FieldDesc from '@components/CustomerFormRender/CustomerFormFieldDesc';
import { isArray, isBoolean, isEmpty, isObject } from 'lodash-es';
import { ReactNode } from 'react';
import {
  DEFAULT_VALUE,
  HiddenType,
  IAceFormConfigItem,
  ICustomerFormConfigItem,
  ICustomerOptions,
  IDefaultValue,
  IEffectValueInfo,
  ITransferFormSchema,
  RenderType,
  ITransFiledType,
  TransferFormSchemaResponse,
  ARRAY_FILED_CONTS,
  CustomerFormType,
} from '@common/constants/formSchema';
import { formatFormSchemaWithStarlingKey } from './starlingKey';
import { TransType } from '@http_idl/infoService';
import CustomerTestData from '@components/CustomerFormRender/CustomerTestData';

const formatFormType = (
  formatInfo: IAceFormConfigItem,
  item: ICustomerFormConfigItem,
): void => {
  formatInfo.defaultType = item.type;
  if (item?.type === 'array') {
    formatInfo.itemsRender = (api, components, index): ReactNode => (
      <ArrayItemsRender
        api={api}
        components={components}
        index={index}
        text={item?.arrayButtonText}
      />
    );
    formatInfo.contentRender = (api, components): any => (
      <ArrayContentRender
        api={api}
        components={components}
        text={item?.arrayButtonText}
      />
    );
    formatInfo.render = (api, component): any => (
      <ArrayRender api={api} components={component} />
    );
  }
  if (item?.type === CustomerFormType.SEARCH_API) {
    formatInfo.type = api =>
      function Customer({ value, onChange }): ReactNode {
        return (
          <CustomerFormApiAccess
            api={api}
            value={value}
            onChange={onChange}
            props={item?.props}
            options={item?.options}
          />
        );
      };
  }
  if (item?.type === CustomerFormType.TEST_DATA) {
    formatInfo.type = api =>
      function Customer({ value, onChange }): ReactNode {
        return (
          <CustomerTestData
            api={api}
            value={value}
            onChange={onChange}
            item={item}
          />
        );
      };
  }

  formatInfo.itemRender = (api, component): ReactNode => (
    <>
      {' '}
      {component} <FieldDesc desc={item?.fieldDesc} />{' '}
    </>
  );
};

const formatFormOptions = (
  formatInfo: IAceFormConfigItem,
  item: ICustomerFormConfigItem,
): void => {
  const { options } = item || {};
  if (isObject(options) && !isArray(options)) {
    const { key, data } = options as ICustomerOptions;
    formatInfo.options = (api): Array<any> => data[api.getValue(key)] || [];
  }
};

const formatFormHidden = (
  formatInfo: IAceFormConfigItem,
  item: ICustomerFormConfigItem,
): void => {
  const { hidden } = item || {};
  try {
    if (isObject(hidden)) {
      const { list, type } = hidden as any;
      const hiddenConnectType = type === '||' ? '||' : '&&';
      const hiddenString = list.reduce((pre, acc, index) => {
        pre += `api.getValue('${acc?.key}')${acc?.type === HiddenType.equal ? '===' : '!=='}'${acc?.value}'${index === list.length - 1 ? ' ; ' : hiddenConnectType}`;
        return pre;
      }, '');
      // eslint-disable-next-line no-eval
      formatInfo.hidden = eval(`(api) => ${hiddenString}`) as any;
    }
  } catch (err) {
    console.log(err);
  }
};

const formatFormDisabled = (
  formatInfo: IAceFormConfigItem,
  item: ICustomerFormConfigItem,
  isEdit: boolean,
): void => {
  const { isEditable } = item || {};
  if (isBoolean(isEditable)) {
    formatInfo.props.disabled = isEdit ? !isEditable : false;
  }
};

const formatEffectValueList = (
  effectValueList: Array<IEffectValueInfo>,
  item: ICustomerFormConfigItem,
): void => {
  if (isArray(item?.effectInfo)) {
    effectValueList.push({
      key: item?.key,
      effectInfo: item?.effectInfo,
    });
  }
};

const formatTransTypeList = (
  fieldMap: ITransFiledType,
  item: ICustomerFormConfigItem,
): void => {
  if (item?.type === 'array') {
    const { items } = item || {};
    items.forEach(arrayItem => {
      // 目前只支持两层
      if (arrayItem?.transType === TransType.ToC) {
        fieldMap.toC.push(`${item?.key}${ARRAY_FILED_CONTS}${arrayItem?.key}`);
      }
    });
  }
  if (item?.transType === TransType.ToC) {
    fieldMap.toC.push(item?.key);
  }
  if (item?.transType === TransType.ToB) {
    fieldMap.toB.push(item?.key);
  }
};

const formatInitValue = (
  valueInfo: any,
  item: ICustomerFormConfigItem,
): void => {
  try {
    if (item?.type === CustomerFormType.TEST_DATA && !isEmpty(item?.items)) {
      const initItems = item?.items || ([] as any[]);
      const itemsValue = {};
      initItems?.forEach(op => {
        itemsValue[`\${${op?.field}}`] = op?.value;
      });
      valueInfo[item?.key] = itemsValue;
    } else {
      valueInfo[item?.key] =
        item?.initValue || DEFAULT_VALUE[item?.type as RenderType];
    }
  } catch (e) {
    console.log('error', e);
  }
};

const formatSchemaItem = (
  item: ICustomerFormConfigItem,
  isEdit: boolean,
  valueInfo: IDefaultValue,
  effectValueList: Array<IEffectValueInfo>,
  fieldMap: ITransFiledType,
): IAceFormConfigItem => {
  try {
    const formatInfo: any = {
      props: {
        ...item.props,
      },
    };

    // 解析type
    formatFormType(formatInfo, item);

    // 解析options
    formatFormOptions(formatInfo, item);

    // 解析hidden
    formatFormHidden(formatInfo, item);

    // 解析是否可编辑
    formatFormDisabled(formatInfo, item, isEdit);

    // 针对依赖项数据变更清空进行解析
    formatEffectValueList(effectValueList, item);

    // 获取字段中toc，tob字段
    formatTransTypeList(fieldMap, item);

    // 返回所有init value 数据
    formatInitValue(valueInfo, item);

    return {
      ...item,
      ...formatInfo,
    };
  } catch (err) {
    return {
      type: 'input',
      key: item?.key,
      label: item?.label || '解析失败',
    };
  }
};

export const transferFormSchema = (
  props: ITransferFormSchema,
): TransferFormSchemaResponse => {
  try {
    const { schema, isEdit, manageType } = props;
    const jsonSchema = formatFormSchemaWithStarlingKey({
      schema,
      manageType,
    });
    const effectValueList = [];
    const valueInfo = {};
    const fieldMap = {
      toC: [],
      toB: [],
    };
    const formSchema = jsonSchema.map(item =>
      formatSchemaItem(item, isEdit, valueInfo, effectValueList, fieldMap),
    );

    return {
      schema: formSchema,
      value: valueInfo,
      fieldMap,
      effectValueList,
      isSuccess: true,
    };
  } catch (err) {
    return {
      schema: [],
      value: {},
      effectValueList: [],
      fieldMap: {
        toC: [],
        toB: [],
      },
      isSuccess: false,
    };
  }
};

export const transferViewSchema = (viewSchemaItemList: any[]) => {
  try {
    const viewSchema = viewSchemaItemList.map(item => {
      item.props.disabled = true;
      if (item.defaultType === CustomerFormType.SEARCH_API) {
        item.type = 'select';
      }
      if (item.defaultType === CustomerFormType.TEST_DATA) {
        item.type = api =>
          function Customer({ value, onChange }): ReactNode {
            return (
              <CustomerTestData
                isView={true}
                api={api}
                value={value}
                onChange={onChange}
                item={item}
              />
            );
          };
      }
      if (item.defaultType === 'array') {
        item.itemsRender = (api, components, index): ReactNode => (
          <ArrayItemsRender
            disabled={true}
            api={api}
            components={components}
            index={index}
            text={item?.arrayButtonText}
          />
        );
        item.contentRender = (api, components): any => (
          <ArrayContentRender
            disabled={true}
            api={api}
            components={components}
            text={item?.arrayButtonText}
          />
        );
        item.render = (api, component): any => (
          <ArrayRender disabled={true} api={api} components={component} />
        );
        item.items.map(child => {
          child.props.disabled = true;
          return child;
        });
      }
      return item;
    });
    return viewSchema;
  } catch (err) {
    return [];
  }
};
