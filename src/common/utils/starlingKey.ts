import { ARRAY_FILED_CONTS } from '@common/constants/formSchema';
import I18n from '@ies/starling_intl';
import { get, isArray, isEmpty, isObject } from 'lodash-es';
import murmur from 'murmurhash-js';

export enum SchemaType {
  SELECT = 'select',
  PAGE = 'page',
}

export enum ManageType {
  INFO = 'info',
  API = 'api',
  COMMON = 'common',
}
interface IFormatSchemaWithStarlingKey {
  schema: string;
  schemaType: SchemaType;
  manageType: ManageType;
}

const formatUniCode = (label: string) => label?.charCodeAt(0)?.toString();

export const formatSchemaWithStarlingKey = (
  props: IFormatSchemaWithStarlingKey,
) => {
  const { schema, schemaType, manageType } = props;
  const prefix = `${manageType}_${schemaType}_schema`;
  let result = [];
  try {
    result = JSON.parse(schema);
  } catch (e) {
    result = [];
  }
  try {
    result.map(item => {
      const { key, label, props: itemProps } = item;
      const { options } = itemProps || {};
      if (label) {
        item.label = I18n.t(
          `${prefix}_${key}_${formatUniCode(label)}`,
          {},
          label,
        );
      }
      if (isArray(options)) {
        options.map(op => {
          const { label: opLabel, value: opValue } = op;
          op.label = I18n.t(
            `${prefix}_${key}_${opValue}_${formatUniCode(opLabel)}`,
            {},
            opLabel,
          );
        });
      }
    });
    return result;
  } catch (err) {
    return [];
  }
};

interface IFormatFormSchemaWithStarlingKey {
  schema: string;
  manageType: ManageType;
}
const formatCommonStarlingKey = (formData, prefix) => {
  const { key, label, props, rules, options, arrayButtonText, items } =
    formData || {};
  const { placeholder } = props || {};
  if (key) {
    if (label) {
      formData.label = I18n.t(
        `${prefix}_${key}_${formatUniCode(label)}`,
        {},
        label,
      );
    }
    if (placeholder) {
      formData.props.placeholder = I18n.t(
        `${prefix}_${key}_${formatUniCode(placeholder)}`,
        {},
        placeholder,
      );
    }
    if (isArray(rules)) {
      rules.map((rule, index) => {
        const { message } = rule;
        if (message) {
          rule.message = I18n.t(
            `${prefix}_${key}_${index}_${formatUniCode(message)}`,
            {},
            message,
          );
        }
      });
    }
    if (isArray(options)) {
      options.map(op => {
        const { label: opLabel, value: opValue } = op;
        if (opLabel && opValue) {
          op.label = I18n.t(
            `${prefix}_${key}_${opValue}_${formatUniCode(opLabel)}`,
            {},
            opLabel,
          );
        }
      });
    }
    if (isObject(options)) {
      const { data } = options || {};
      for (const opData in data) {
        const dataValue = data[opData];
        if (isArray(dataValue)) {
          dataValue.map(dataOp => {
            const { label: dataOpLabel, value: dataOpValue } = dataOp || {};
            if (dataOpLabel && dataOpValue) {
              dataOp.label = I18n.t(
                `${prefix}_${key}_${opData}_${dataOpValue}_${formatUniCode(label)}`,
                {},
                dataOpLabel,
              );
            }
          });
        }
      }
    }
    if (arrayButtonText) {
      formData.arrayButtonText = I18n.t(
        `${prefix}_${key}_${formatUniCode(arrayButtonText)}`,
        {},
        arrayButtonText,
      );
    }
    if (isArray(items)) {
      items.map(itemsItem => {
        formatCommonStarlingKey(itemsItem, prefix);
      });
    }
  }
};
/**
 * 将表单schema中的label转换成starling Key形式
 * @param props
 * @returns
 */
// const MOCKCustomerSchema = {
//   label: '测试数据',
//   type: 'customer-test-data',
//   key: 'testData', // 假设: testData
//   items: [{ // 可选，用于字段回填
//     label: '订单ID',
//     field: 'orderId',
//     value: '2342123',
//   }, {
//     label: '用户ID',
//     field: 'userId',
//     value: '123213123123'
//   }],
//   isEditable: true // 如果为false，则不可点击
// };
export const formatFormSchemaWithStarlingKey = (
  props: IFormatFormSchemaWithStarlingKey,
) => {
  const { schema, manageType } = props;
  const prefix = `${manageType}_form_schema`;
  let result = [];
  try {
    result = JSON.parse(schema);
    // result.push(MOCKCustomerSchema);
  } catch (e) {
    result = [];
  }
  try {
    result.map(item => formatCommonStarlingKey(item, prefix));
    return result;
  } catch (e) {
    return [];
  }
};

/**
 *
 * 通过字符串生成10位数字的Key
 * @param {string} text
 * @return {*}  {string}
 */
export const genKey = (text: string, namespaceId: string): string =>
  `${namespaceId || 'INIT'}-${murmur.murmur3(text, 10)}`;

/**
 *
 * @param {string[]} strList
 * @return {*}
 */
export const strListToI18nStr = (
  strList: string[],
  namespaceId: string,
): string => {
  try {
    const i18nData = {};
    strList.forEach(item => {
      i18nData[genKey(item, namespaceId)] = item;
    });
    return JSON.stringify({
      type: 'map',
      data: i18nData,
    });
  } catch (e) {
    console.error('qi::strListToI18nStr', e);
    return '';
  }
};

/**
 * 将value中的字段区分ToC与ToB，返回携带starling Key的字符串
 * {
 *   listMap: {},
 *   schema: string;
 * }
 */

export const getValuesStarling = (
  filedList: string[],
  values: any,
  namespaceId: string,
): any => {
  const listMap = {};
  const strList = [];
  filedList.map(item => {
    const isArrayField = item.indexOf(ARRAY_FILED_CONTS) !== -1;
    if (!isArrayField) {
      // 证明是单值，直接注入数据就可以
      const value = get(values, item);
      if (value) {
        listMap[item] = {
          label: value,
          strKey: genKey(value, namespaceId),
        };
        strList.push(value);
      }
    } else {
      // 证明是动态数据，需要获取数组类型
      // 目前只支持两层
      const [key, itemKey] = item.split(ARRAY_FILED_CONTS);
      const keyValues = get(values, key);
      keyValues.forEach((kv, ki) => {
        const arrayValue = get(kv, itemKey);
        if (arrayValue) {
          listMap[`${key}.${ki}.${itemKey}`] = {
            label: arrayValue,
            strKey: genKey(arrayValue, namespaceId),
          };
          strList.push(arrayValue);
        }
      });
    }
  });
  return {
    listMap,
    schema: strListToI18nStr(strList, namespaceId),
  };
};

export const getFieldType = (filedMap: any) => {
  try {
    const { toC, toB } = filedMap || {};
    const isHaveAgent = !isEmpty(toB);
    const isHaveUser = !isEmpty(toC);
    return {
      isHaveAgent,
      isHaveUser,
    };
  } catch (e) {
    return {
      isHaveAgent: false,
      isHaveUser: false,
    };
  }
};
