// 将从后端获取到的列表数据进行转换
import { Data } from '@http_idl/infoService';

/**
 * @description: 将从后端获取的列表内容数据转换成指定的数据格式
 * @param {Array} propsData: 从后端接口处获取的列表数据
 * @return {*}
 */
const transferData = (propsData: Array<Array<Data>>): Array<any> => {
  const data: any[] = [];
  propsData.forEach(item => {
    let obj = {};
    // 如果某一行内容为空，返回空数组
    if (!item?.length) {
      return;
    }
    item.forEach(ele => {
      // 如果某个值的key为空，直接为空
      if (!ele.key) {
        return;
      }
      // 如果某一data的类型为“string”类型
      if (ele.type === 'string') {
        obj = { ...obj, [ele.key]: ele.value || '-' };
        return;
      }
      // 如果某一data的类型为非“string”类型
      obj = { ...obj, [ele.key]: { value: '-', ...ele } };
    });
    data.push(obj);
  });
  return data;
};
export default transferData;
