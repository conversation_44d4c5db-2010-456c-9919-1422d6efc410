import { useEffect, useRef } from 'react';

export const useDidMount = fn => useEffect(() => fn && fn(), []);

export const useDidUpdate = (fn, conditions): void => {
  const didMoutRef = useRef(false);
  useEffect(() => {
    if (!didMoutRef.current) {
      didMoutRef.current = true;
      return;
    }
    // Cleanup effects when fn returns a function
    return fn && fn();
  }, conditions);
};

export const useWillUnmount = fn => useEffect(() => () => fn && fn(), []);
