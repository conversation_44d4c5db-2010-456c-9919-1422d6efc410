import { isString, isObject } from 'lodash-es';

/**
 * @description: 为部分项的value做兜底（如果后端传过来的特殊类型的字段对应的type不是特殊类型，按照一般类型做兜底处理）
 * @param any
 * @return {*}
 */
const transferValue = (data: any): string => {
  if (isString(data)) {
    return data;
  } else if (isObject(data) && data?.value) {
    return `${data?.value}`;
  } else {
    return JSON.stringify(data);
  }
};

export default transferValue;
