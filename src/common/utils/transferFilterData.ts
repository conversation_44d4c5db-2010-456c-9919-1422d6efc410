// 将筛选项的数据转换成string类型
import { isString, isObject } from 'lodash-es';
interface TimeProps {
  from: string;
  to: string;
}
interface PeopleProps {
  name: string;
  email: string;
  uuid: string;
}
type FilterDataType = string | Array<string> | TimeProps | Array<PeopleProps>;

/**
 * @description: 将筛选项变化后的数据转换成string
 * @param {FilterDataType} value: 筛选项变化后的数据
 * @return {*}
 */
const transferFilterData = (value: FilterDataType): string => {
  if (isString(value)) {
    return value;
  } else if (Array.isArray(value)) {
    if (value.length && isObject(value[0])) {
      value = value.map(item => item.email || '');
    }
    return value.join(',');
    // eslint-disable-next-line no-prototype-builtins
  } else if (isObject(value) && value.hasOwnProperty('from')) {
    return `${new Date(value.from).getTime()},${new Date(value.to).getTime()}`;
  } else {
    return JSON.stringify(value);
  }
};

export default transferFilterData;
