import { ManageType } from '@common/utils/starlingKey';
import { TransType } from '@http_idl/infoService';
import { ReactNode } from 'react';

export const enum CustomerFormType {
  TEST_DATA = 'customer-test-data', // 自定义测试组件
  SEARCH_API = 'remote-search-api',
}

export type RenderType =
  | CustomerFormType.SEARCH_API
  | CustomerFormType.TEST_DATA
  | 'input'
  | 'textarea'
  | 'select'
  | 'radio'
  | 'array'
  | 'checkbox';

export enum HiddenType {
  Equal = 'equal',
  UnEqual = 'UnEqual',
}

export interface IHidden {
  key: string;
  value: string;
  type: HiddenType;
}

export interface IOptions {
  label: string; // 筛选项名称
  value: string; // 筛选项内容
  children?: Array<IOptions>; // 树形结构使用
}

export interface ICustomerOptions {
  key: string; // 动态判断的Key
  data: {
    [key: string]: Array<IOptions>; // key为动态key下的数据，根据动态key所获取内容
  };
}

export interface IRules {
  required?: boolean; // 是否必填提示
  type?: 'number' | 'boolean'; // 必须是数字、布尔
  pattern?: string; // 匹配校验正则表达式
  max?: number; // 最大长度提示
  message?: string; // 错误提示文案
}

export interface IEffectInfo {
  key: string;
  value: string;
}

export interface IProps {
  placeholder: string; // 占位符
  showClear?: boolean; // 筛选项使用，支持清空
  disabled?: boolean; // 是否禁用
  clickToHide?: boolean; // 单选框使用，点击即关闭,需要传入true
  filter?: boolean; // 选择框支持搜素
  multiple?: boolean; // 多选框
  maxTagCount?: number; // 多选框最多展示几条，超出部分+N，通常为2
  getPopupContainer?: () => HTMLElement; // select 指定父级 DOM，弹层将会渲染至该 DOM 中，自定义需要设置 position: relative，前端处理。
  style?: {
    width?: number; // 组件长度，可以找前端确认提供
  };
  className?: string; // 当样式为INFO类型或INFO子类型的单选卡片样式时，需要写入className
  // radio-card-1，radio-card-2，radio-card-more
}

export interface IAceFormConfigItem {
  label?: string; // 字段描述
  type?: RenderType | (() => ReactNode); // 组件类型，文本框、单选、数字、单选、动态添加
  defaultType?: any; // 用于存储后端返回的type
  key?: string; // 接口字段名称
  items?: Array<IAceFormConfigItem>; // 当type为array时，需要传入
  rules?: Array<IRules>;
  transType?: TransType;
  options?: Array<IOptions> | ICustomerOptions | ((api) => Array<any>);
  hidden?: {
    type: string;
    list: Array<IHidden>;
  }; // 当所设置的key的value与设置的value相等或不相等时，隐藏
  itemsRender?: (api, components, index) => ReactNode;
  contentRender?: (api, components) => ReactNode;
  render?: (api, components) => ReactNode;
  itemRender?: (api, components) => ReactNode;
  props?: IProps;
}

export interface ICustomerFormConfigItem extends IAceFormConfigItem {
  isEditable: boolean; // 编辑状态下是否可编辑
  initValue?: any; // 当类型为array时，默认的value应该为对象数组，key前端不可控
  effectInfo?: Array<IEffectInfo>; // 设置了这个值，当当前key的value发生改变时
  transType?: TransType;
  // 会将effectInfo里设置的key的实际value重新设置为给定的value值
  fieldDesc?: string; // 表单字段下方描述文案
  arrayButtonText?: string; // 当type为array类型时写入，用于控制动态增减列表的button文案。
}

export interface IDefaultValue {
  [key: string]: any;
}

export interface IEffectValueInfo {
  key: string;
  effectInfo: Array<IEffectInfo>;
}
export interface FormConfig {
  formSchema: ICustomerFormConfigItem[];
}

export interface FormFieldMap {
  toC: Array<string>;
  toB: Array<string>;
}

export interface TransferFormSchemaResponse {
  schema: IAceFormConfigItem[];
  value: IDefaultValue;
  effectValueList: Array<IEffectValueInfo>;
  isSuccess: boolean;
  fieldMap: FormFieldMap;
}

export interface ITransferFormSchema {
  schema: string;
  manageType: ManageType;
  isEdit: boolean;
}

export interface ITransFiledType {
  toC: Array<string>;
  toB: Array<string>;
}

export const DEFAULT_VALUE = {
  input: '',
  textarea: '',
  select: '',
  radio: '',
  [CustomerFormType.SEARCH_API]: undefined, // 不为空字符串的原因是，自定义remote select value为空时，不展示placeholder
  [CustomerFormType.TEST_DATA]: undefined,
  array: [],
};

export const ARRAY_FILED_CONTS = '.Array.';

export const TOOLS_TYPE_API_LIST = [1, 4];
