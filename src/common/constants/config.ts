export default {
  /**
   * 应用数据统计
   * 接入指南 https://bytedance.feishu.cn/docs/doccnbxDbCUicHF1w6oxLDY7Xlh
   * 创建应用后，替换app_id即可
   */
  tea: {
    // 数据统计
    app_id: 1663,
    channel: 'va',
  },
  /**
   * 国际化文案管理
   * 新产品接入 https://doc.bytedance.net/docs/67/92/1606/#%E5%A6%82%E4%BD%95%E5%BC%80%E5%A7%8B%EF%BC%9F
   * 创建应用后，替换api_key即可
   */
  starling: {
    // 国际化
    api_key: 'f2a1af00326911ecafcf3de0ce3c24d5',
    namespace: [
      'FE_BASE',
      'API_SELECT_SCHEMA',
      'API_PAGE_SCHEMA',
      'API_FORM_SCHEMA',
      'INFO_SELECT_SCHEMA',
      'INFO_PAGE_SCHEMA',
      'INFO_FORM_SCHEMA',
    ], // namespace
    locale: 'zh', // 当前语言
    zoneHost: 'https://starling-oversea.byteoversea.com', // 文案获取接口域名，v3 新增
    TEAChannelDomain: 'https://sgali-mcs.byteoversea.com', // sdk 内部 tea 数据打点域名，v3 新增
    test: false, // 是否测试环境
    timeout: 10000, // 单位ms,默认3000ms
    lazyUpdate: false, // 先渲染页面，文案更新延迟到下次页面加载,
    fallbackLang: ['zh', 'en'], // 降级语言列表，当前语言没有数据时降级到该列表，从左往右
  },
  /**
   * 反馈工单系统
   * 接入文档 https://bytedance.feishu.cn/space/doc/doccnYf10eu03xG1v2ZtNQ
   * https://issue.bytedance.net/survey/dashboard 创建应用后，替换appKey即可
   */
  hornbill: {
    appKey: 30,
  },
};
