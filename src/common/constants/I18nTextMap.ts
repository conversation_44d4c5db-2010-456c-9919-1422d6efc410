/* eslint-disable @typescript-eslint/naming-convention */
import I18n from '@ies/starling_intl';

// 由前端管理维护的多语言文案 START

// 空间地址 https://starling.bytedance.net/project_detail/8443/space/50657#lang
export const INFO_DEBUG_TEXT_MAP = {
  TITLE: (name: string) => I18n.t('info_debug_title', { name }),
  DEBUG_INFO: I18n.t('info_debug'),
  LIVE_DEBUG: I18n.t('info_debug_live_debug'),
  LOG_ANALYSIS: I18n.t('info_debug_log_analysis'),
  DEPLOY_REGION: I18n.t('info_debug_deploy_region'),
  CALLER_PSM: I18n.t('info_debug_caller_psm'),
  CALLER_PSM_EXTRA: I18n.t('info_debug_caller_psm_extra'),
  ARGOS_LINK: I18n.t('info_debug_argos_link'),
  CALLER: I18n.t('info_debug_caller'),
  DATA_MARKET: I18n.t('info_debug_datamarket'),
  SCRIPT: I18n.t('info_debug_script'),
  DOWNSTREAM: I18n.t('info_debug_downstream'),
  // 请求数据
  REQUEST_DATA: I18n.t('info_debug_request_data'),
  // 执行记录
  EXECUTION_RECORD: I18n.t('info_debug_execution_record'),
  // 执行记录中包含当前Log ID下所有报错，不区分Info Key
  EXECUTION_RECORD_DESC: I18n.t('info_debug_execution_record_desc'),
  // 执行概览
  EXECUTION_OVERVIEW: I18n.t('info_debug_execution_overview'),
  // 数据测试成功
  DATA_TEST_SUCCESS: I18n.t('info_debug_data_test_success'),
  // 数据测试失败
  DATA_TEST_FAIL: I18n.t('info_debug_data_test_fail'),
  // 暂未运行
  NOT_RUN_YET: I18n.t('info_debug_not_run_yet'),
  // 已复制到剪贴板
  COPIED_TO_CLIPBOARD: I18n.t('info_debug_copied_to_clipboard'),
  // 复制失败
  COPY_FAILED: I18n.t('info_debug_copy_failed'),
  // 复制入参的JSON格式，可用于QueryByInfoKeyV2请求
  COPY_PARAMS_JSON_TOOLTIP: I18n.t('info_debug_copy_params_json_tooltip'),
  // 入参JSON已复制到剪贴板
  PARAMS_JSON_COPIED: I18n.t('info_debug_params_json_copied'),
  // 复制入参JSON失败
  PARAMS_JSON_COPY_FAILED: I18n.t('info_debug_params_json_copy_failed'),
  // Fixed Fields
  FIXED_FIELDS: I18n.t('info_debug_fixed_fields'),
  // 运行数据
  RUN_DATA: I18n.t('info_debug_run_data'),
  // 请求 {idx + 1}
  REQUEST_NUMBER: (idx: number) => I18n.t('info_debug_request_number', { idx }),
  // 请求参数
  REQUEST_PARAMS: I18n.t('info_debug_request_params'),
  // Info数据返回
  INFO_DATA_RETURN: I18n.t('info_debug_info_data_return'),
  // 运行结果
  RUN_RESULT: I18n.t('info_debug_run_result'),
  // 无数据返回
  NO_DATA_RETURN: I18n.t('info_debug_no_data_return'),
  // 错误详情
  ERROR_DETAILS: I18n.t('info_debug_error_details'),
  // 原始数据返回
  RAW_DATA_RETURN: I18n.t('info_debug_raw_data_return'),
  // 从日志获取入参数据
  GET_DATA_FROM_LOG: I18n.t('info_debug_get_data_from_log'),
  // 获取logid中该info key请求的入参数据
  GET_DATA_FROM_LOG_DESC: I18n.t('info_debug_get_data_from_log_desc'),
  // 输入需要查询的Log ID
  INPUT_LOG_ID_TIP: I18n.t('info_debug_input_log_id_tip'),
  // 选择日志机房
  SELECT_LOG_REGION_TIP: I18n.t('info_debug_select_log_region_tip'),
  // 从日志中获取请求数据
  GET_REQUEST_DATA_FROM_LOG: I18n.t('info_debug_get_request_data_from_log'),
  // Info Keys
  INFO_KEYS: I18n.t('info_debug_info_keys'),
  // 返回数据
  RESPONSE_DATA: I18n.t('info_debug_response_data'),
  // Info Key成功返回且有值
  RESPONSE_SUCCESS_WITH_VALUE: I18n.t('info_debug_response_success_with_value'),
  // Info Key返回但无值
  RESPONSE_SUCCESS_NO_VALUE: I18n.t('info_debug_response_success_no_value'),
  // Info Ke被请求但未返回，请检查Info Key是否正确
  RESPONSE_NOT_RETURNED: I18n.t('info_debug_response_not_returned'),
  // 选择Info Key查看值
  SELECT_INFO_KEY_TO_VIEW_VALUE: I18n.t(
    'info_debug_select_info_key_to_view_value',
  ),
  // 当前Info Key无返回值
  NO_VALUE_RETURNED: I18n.t('info_debug_no_value_returned'),
  // 请求或返回中未发现Info key
  INFO_KEY_NOT_FOUND: (key: string) =>
    I18n.t('info_debug_info_key_not_found', { key }),
  // 输入需要分析的日志log id
  INPUT_ANALYSIS_LOG_ID_TIP: I18n.t('info_debug__analysis_input_log_id_tip'),
  // 目前仅支持分析ROW地区的日志
  INPUT_ANALYSIS_REGION_TIP: I18n.t('info_debug_analysis_input_region_tip'),
  // 执行错误
  EXECUTION_ERROR: I18n.t('info_debug_execution_error'),
  // 执行成功
  EXECUTION_SUCCESS: I18n.t('info_debug_execution_success'),
  // 无法获得执行结果
  EXECUTION_NO_RESULT: I18n.t('info_debug_execution_no_result'),
  // 数据中心相关报错请联系研发
  CONTACT_DEVELOPER: I18n.t('info_debug_contact_developer'),
  // 无错误详情
  NO_ERROR_DETAILS: I18n.t('info_debug_no_error_details'),
  // 请求 ID
  REQ_ID: (id: string) => I18n.t('info_debug_req_id', { id }),
  // 无数据
  NO_DATA: I18n.t('info_debug_no_data'),
  // 未找到日志，请检查日志ID是否正确。
  LOG_NOT_FOUND: I18n.t('info_debug_log_not_found'),
  // 日志获取成功
  LOG_FETCH_SUCCESS: I18n.t('info_debug_log_fetch_success'),
  // 日志获取失败
  LOG_FETCH_FAIL: I18n.t('info_debug_log_fetch_fail'),
};

export const INFO_FE_TEXT_MAP = {
  Title: I18n.t('info_title'),
  Desc: I18n.t('info_desc'),
  Create: I18n.t('info_create'),
  Disable_Error_Tips: I18n.t('info_disable_error_tips'),
  Able_Error_Tips: I18n.t('info_enable_error_tips'),
  Enable_Tips: I18n.t('info_enable_tips'),
  Back_Tips: I18n.t('info_back'),
  // 分析调用Info的日志内容，快速判断故障点和错误原因
  DEBUG_TIP: I18n.t('info_debug_entrance_tip'),
};

export const API_FE_TEXT_MAP = {
  Title: I18n.t('api_title'),
  Desc: I18n.t('api_desc'),
  Create: I18n.t('api_create'),
  Disable_Fail_Tips: I18n.t('api_disable_fail_tips'),
  Enable_Fail_Tips: I18n.t('api_enable_fail_tips'),
  Enable_Tips: I18n.t('api_enable_tips'),
  Back_Tips: I18n.t('api_back'),
};

export const COMMON_FE_TEXT_MAP = {
  Divert: I18n.t('common_divert'),
  Translation: I18n.t('common_translation'),
  Release: I18n.t('common_release'),
  Confirm: I18n.t('common_confirm'),
  Save: I18n.t('common_save'),
  Ok: I18n.t('common_ok'),
  Cancel: I18n.t('common_cancel'),
  Disable: I18n.t('common_disable'),
  Edit: I18n.t('common_edit'),
  Enable: I18n.t('common_enable'),
  Success: I18n.t('common_success'),
  Create: I18n.t('common_create'),
  Update: I18n.t('common_update'),
  DEBUG: I18n.t('common_debug'),
  Fail: I18n.t('common_fail'),
  Quit: I18n.t('common_quit'),
  Delete: I18n.t('common_delete'),
  Trans_Placeholder: I18n.t('common_trans_placeholder'),
  Placeholder_Search_Select: I18n.t('common_placeholder_search_select'),
  Placeholder_Search_People: I18n.t('common_placeholder_search_people'),
  Placeholder_Search_Time: I18n.t('common_placeholder_search_time'),
  Placeholder_Enter_Text: I18n.t('common_placeholder_enter_text'),
  Trans_Tips: I18n.t('common_trans_tips'),
  Trans_Rules_Msg: I18n.t('common_trans_rulesMsg'),
  Operation_Record: I18n.t('common_operation_record'),
  Fetch_Operation_Record_Fail: I18n.t('common_fetch_operation_record_fail'),
  Fetch_More_Operation_Record: I18n.t('common_fetch_more_operation_record'),
  Operation_Record_Empty: I18n.t('common_operation_record_empty'),
  Empty_Data: I18n.t('common_empty_data'),
  Operate_Time: I18n.t('common_operate_time'),
  Form_Check_Error: I18n.t('common_form_check_error'),
  Form_Save_Success: I18n.t('common_form_save_success'),
  Form_Trans_Fail: I18n.t('common_form_trans_fail'),
  Form_Trans_Success: I18n.t('common_trans_success'),
  Form_Save_Loading: I18n.t('common_form_loading'),
  Choose_API: I18n.t('common_choose_api'),
  Save_Success: I18n.t('common_save_success'),
  Save_Fail: I18n.t('common_save_fail'),
  Create_Success: I18n.t('common_create_success'),
  Create_Fail: I18n.t('common_create_fail'),
  Quit_Tips: I18n.t('common_quit_tips'),
  No_Form_Data: I18n.t('common_no_form_data'),
  No_Form_Data_Tips: I18n.t('common_no_form_data_tips'),
  No_Perms: I18n.t('common_no_perms'),
  No_Perms_Tips: I18n.t('common_no_perms_tips'),
  Page_Error: I18n.t('common_page_error'),
  Page_Error_Operation: I18n.t('common_page_error_operation'),
  List_Empty: I18n.t('common_list_empty'),
  Status_InConfig: I18n.t('common_status_inconfig'),
  Status_Enable: I18n.t('common_status_enable'),
  Status_Disable: I18n.t('common_status_disable'),
  Trans_Check_Fail: I18n.t('common_trans_check_fail'),
  Run_Data: I18n.t('common_run_data'),
  Test_Success: I18n.t('common_test_success'),
  Test_Fail: I18n.t('common_test_fail'),
  Getting_Data: I18n.t('common_getting_data'),
  Get_Data_Auto: I18n.t('common_get_data_auto'),
  Get_Data_Fail_Tips: I18n.t('common_get_data_fail_tips'),
  SEARCH: I18n.t('common_search'),
  STOP_SEARCH: I18n.t('common_stop_search'),
  REQUIRED_FIELD: (name: string) => I18n.t('common_required_field', { name }),
  Input_Placeholder_Name: (name: string) =>
    I18n.t('common_input_placeholder_by_name', { name }),
  Prompt_Message: (title: string) => I18n.t('common_prompt_message', { title }),
  Delete_Success_Name: (name: string) =>
    I18n.t('common_delete_success_by_name', { name }),
  Delete_Fail_Name: (name: string) =>
    I18n.t('common_delete_fail_by_name', { name }),
  Confirm_Quit: (title: string) => I18n.t('common_quit_tips', { title }),
  Enable_Success: (name: string, type: string) =>
    I18n.t('common_enable_success_tips', { name, type }),
  Enable_Error: (name: string, type: string) =>
    I18n.t('common_enable_fail_tips', { name, type }),
  Disable_Error_Title: (name: string) =>
    I18n.t('common_disable_error_title', { title: name }),
  Enable_Error_Title: (name: string) =>
    I18n.t('common_enable_error_title', { title: name }),
  Enable_Name: (name: string) => I18n.t('common_enable_name', { name }),
  Delete_Name: (name: string) => I18n.t('common_delete_by_name', { name }),
  Edit_Name: (name: string) => I18n.t('common_edit_name', { name }),
  // 收起
  Collapse: I18n.t('common_collapse'),
  // 展开
  Expand: I18n.t('common_expand'),
};

// 由前端管理维护的多语言文案 END

export enum TranslateButtonType {
  AGENT,
  USER,
}
