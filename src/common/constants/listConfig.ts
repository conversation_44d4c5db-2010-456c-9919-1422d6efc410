import { COMMON_FE_TEXT_MAP } from './I18nTextMap';

export enum StatusWords {
  Disable = '已禁用',
  Able = '已启用',
  InConfig = '配置中',
}

export enum DisableOrAbleWords {
  Disable = '禁用',
  Able = '启用',
}

export enum StatusType {
  DISABLE,
  ENABLE,
  INCONFIG,
}

export const STATUS_MAP = {
  [StatusType.INCONFIG]: {
    name: COMMON_FE_TEXT_MAP.Status_InConfig,
    color: 'blue',
  },
  [StatusType.DISABLE]: {
    name: COMMON_FE_TEXT_MAP.Status_Disable,
    color: 'red',
  },
  [StatusType.ENABLE]: {
    name: COMMON_FE_TEXT_MAP.Status_Enable,
    color: 'green',
  },
};

export interface ResColumnsProps {
  label: string;
  key: string;
  type: string;
  props: {
    width?: number;
    fixed?: boolean;
  };
}

interface OptionsProps {
  label: string; // 筛选项名称
  value: string; // 筛选项数组
  children?: Array<OptionsProps>; // 树形结构children
}

export interface ResFilterProps {
  label: string; // 筛选项名称
  key: string; // 筛选项唯一描述符, 也是搜索获取list的参数
  type: string; // 筛选项类型；当前包括搜索，多选，时间区间选择，人员搜索（支持邮箱&人员姓名搜索）
  props?: {
    options?: Array<OptionsProps>; // 当类型为MultiSelect时，需要传入该参数，用来获取选项数据
    defaultValue?: any;
  };
}
