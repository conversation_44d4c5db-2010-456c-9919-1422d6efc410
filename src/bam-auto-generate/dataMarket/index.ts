// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum APaaSQueryType {
  /** 详情查询 */
  QueryDetail = 1,
  /** 分页查询 */
  QueryMore = 2,
  /** 列表查询 */
  QueryAll = 3,
}

export enum CacheConfigStrategy {
  WirteIfNotExists = 1,
  WirteAfterRead = 2,
  ReadOnly = 3,
}

export enum CacheResourceType {
  ABASE = 1,
  REDIS = 2,
}

export enum InfoValueSource {
  MOCK = 0,
  SESSION = 1,
  GQL = 2,
  DEFAULT = 3,
}

export enum OpType {
  /** 查询数据，并记录 */
  Record = 0,
  /** 读DM录制完的数据 */
  PLAY = 1,
}

export enum QueryIDType {
  /** 实际的 */
  RealQueryID = 1,
  AliasName = 2,
}

export enum ResultFormat {
  JsonObj = 1,
  KV = 2,
  JSONStr = 3,
}

export interface ABase {
  ClusterName?: string;
  Table?: string;
}

export interface AddQueryRequest {
  /** 查询语句 */
  Content: string;
  /** 期望的QueryId，不一定会使用此值作为最终QueryId */
  ExpectQueryId?: string;
  /** 查询参数示例 */
  VariablesExample?: string;
  /** 调用方psm */
  Psm?: string;
  /** 生效环境，默认正式环境 */
  EnvList?: Array<string>;
  /** idl schema */
  SchemaId?: string;
}

export interface AddQueryResponse {
  /** 生成的QueryId */
  queryId: string;
}

export interface APaaSActionTestRequest {
  SpaceId?: string;
  ReqConfig?: string;
  DataSourceConfig?: string;
  RespConfig?: string;
  WorkflowStr?: string;
  ApaasMetaData?: string;
  ParamMap?: Record<string, string>;
}

export interface APaaSActionTestResponse {
  Data?: string;
}

export interface APaaSDynamicEnumExecuteRequest {
  /** format: spaceCode.EnumCode */
  EnumCode: string;
  /** 参数Map */
  ParamMap?: Record<string, string>;
}

export interface APaaSDynamicEnumExecuteResponse {
  Data?: Array<DynamicEnumData>;
  DefaultValue?: string;
}

export interface APaaSQuery {
  /** 查询Code */
  QueryCode: string;
  /** 查询空间ID */
  SpaceId: string;
  /** 字段路径 */
  FieldPath?: Array<string>;
  /** 入参 */
  Params?: Record<string, string>;
  /** 场景 */
  SceneCode?: string;
}

export interface APaaSQueryExecuteRequest {
  /** 查询列表 */
  QueryList: Array<APaaSQuery>;
  /** 应用场景 */
  AppCode: string;
}

export interface APaaSQueryExecuteResponse {
  /** 查询结果 */
  QueryResults: Array<APaaSQueryResult>;
  /** 错误 */
  Errors?: Array<string>;
}

export interface APaaSQueryResult {
  FieldPath: string;
  FieldPathValue: string;
}

export interface APaasScene {
  /** APaas空间ID */
  SpaceId: string;
  /** 场景Code */
  SceneCode: string;
}

export interface APaaSWorkflowTestRequest {
  Statemachine?: string;
  WorkflowConfig?: string;
  ParamMap?: Record<string, string>;
}

export interface APaaSWorkflowTestResponse {
  Success: boolean;
  Data?: string;
  ErrorMsg?: string;
}

export interface BatchCommonAPaaSQueryRequest {
  QueryList?: Array<CommonAPaaSQueryReqItem>;
}

export interface BatchCommonAPaaSQueryResponse {
  QueryResultJson?: string;
}

export interface CacheConfig {
  /** 必填，是否开启，默认关闭 */
  Enabled?: boolean;
  /** 选填，缓存策略，默认read_only */
  Strategy?: CacheConfigStrategy;
  /** 选填， 缓存过期时间，当Strategy为 read_only时，可以不填，其他必填 */
  ExpireSecond?: Int64;
  /** 选填，默认共享资源池 */
  CacheResource?: CacheResource;
  /** 选填，要开启 cache的INFOKEY 列表，为空则表示全部 */
  InfoKeys?: Array<string>;
  /** 选填，是否需要返回cache的调试信息 */
  NeedProfilingInfo?: boolean;
}

/** cache的调试信息 */
export interface CacheProfilingInfo {
  /** 如果都没有命中缓存， 则false，否则true */
  IsHit?: boolean;
  /** 命中的总耗时 */
  HitTimeCostMs?: Int64;
  /** 未命中的总耗时 */
  MissedTimeCostMs?: Int64;
  /** 命中的infokey，仅针对仅针对QueryByInfoKeys/QueryByInfoKeysV2 有效 */
  HitInfoKeys?: Array<string>;
}

export interface CacheResource {
  Type?: CacheResourceType;
  Abase?: ABase;
  Redis?: Redis;
}

export interface CheckInfoKeysIsValidRequest {
  InfoKeys: Array<InfoDetail>;
  Variables?: string;
  /** 默认执行 */
  IsOnlyCheckQueryValid?: boolean;
}

export interface CheckInfoKeysIsValidResponse {
  Data?: string;
  Errors?: Array<string>;
  Query: string;
}

export interface CommonAPaaSActionRequest {
  /** 项目空间ID */
  SpaceId: string;
  /** 动作Code */
  ActionCode?: string;
  /** 动作参数, JSON格式 */
  ActionParam?: string;
}

export interface CommonAPaaSActionResponse {
  Data?: string;
  Result?: string;
  ErrorCode?: string;
  ErrorMsg?: string;
}

export interface CommonAPaaSQueryReqItem {
  /** 需要返回的字段路径 */
  OutputFieldPath?: Array<string>;
  /** 入参 */
  InputMap?: Record<string, string>;
  /** 查询类型 */
  QueryType?: APaaSQueryType;
  /** 强制返回左值 */
  IsForceReturnLeftValue?: boolean;
  /** 场景Code */
  SceneCode?: string;
}

export interface CommonAPaaSQueryRequest {
  /** 查询空间ID */
  SpaceId?: string;
  /** 需要返回的字段路径 */
  OutputFieldPath?: Array<string>;
  /** 入参 */
  InputMap?: Record<string, string>;
  /** 废弃字段: 旧场景，传入SpaceId + SceneCode */
  Scene?: APaasScene;
  /** 查询类型 */
  QueryType?: APaaSQueryType;
  /** 返回数据格式 */
  ResultFormat?: ResultFormat;
  /** 强制返回左值 */
  IsForceReturnLeftValue?: boolean;
  /** 新场景，格式spaceCode.sceneCode */
  SceneCode?: string;
}

export interface CommonAPaaSQueryResponse {
  /** JSONObject格式的KV */
  Output?: string;
  Errors?: Array<string>;
  /** list<KV>格式的KV */
  OutputResults?: Array<APaaSQueryResult>;
}

export interface DataQueryRequest {
  Psm: string;
  Token: string;
  Query?: string;
  QueryId?: string;
  Variables?: string;
  UserInfo?: UserInfo;
  IsMustCompleteFieldNotNull?: boolean;
  NewQueryID?: string;
  /** 上游by psm生成的unique id, for 埋点 */
  UniqueId?: string;
  UseNewCache?: boolean;
  /** 100以后国际化专用 */
  CacheConfig?: CacheConfig;
}

export interface DataQueryResponse {
  Data?: string;
  Errors?: Array<string>;
  /** 100 以后国际化专用 */
  CacheProfilingInfo?: CacheProfilingInfo;
}

export interface DynamicEnumData {
  Label?: string;
  Value?: string;
  Disable?: boolean;
  DisableToolTip?: string;
  Children?: Array<DynamicEnumData>;
}

export interface EnableQueryRequest {
  /** id */
  QueryId: string;
  /** 版本号 */
  Version: string;
  /** 生效环境，默认正式环境 */
  EnvList?: Array<string>;
}

export interface EnableQueryResponse {}

/** 新服务加解密相关数据（fieldMask ,传递这个参数请求新夫妇） */
export interface EncryptionParams {
  Platform?: string;
  OperationPage?: string;
  IssueNo?: string;
}

export interface GenericRPCCallRequest {
  PSM: string;
  Method: string;
  RequestStr: string;
  ForTriggerCache?: boolean;
}

export interface GenericRPCCallResponse {
  RespStr?: string;
}

export interface GenQueryByInfoKeysRequest {
  InfoKeys: Array<string>;
  /** 是否需要字段的父级信息 */
  IsNeedParentMap?: boolean;
}

export interface GenQueryByInfoKeysResponse {
  Query?: string;
  ParentMap?: Record<string, string>;
}

export interface GetQueryHistoryListRequest {
  /** id */
  QueryId: string;
  /** 分页Number */
  Page?: number;
  /** 分页大小 */
  Limit?: number;
}

export interface GetQueryHistoryListResponse {
  queryList?: Array<QueryInfo>;
  count?: number;
}

export interface GetQueryInfoRequest {
  /** id */
  QueryId: string;
  /** 生效环境，默认正式环境 */
  Env?: string;
  /** 是否允许使用缓存 */
  NotUseCache?: boolean;
}

export interface GetQueryInfoResponse {
  queryInfo?: QueryInfo;
}

export interface GetQueryListRequest {
  /** 分页Number */
  Page: number;
  /** 分页大小 */
  Limit: number;
  /** 指定环境，默认返回全部环境的 */
  Env?: string;
  /** 是否返回内容 */
  NeedContent?: boolean;
  /** 是否返回参数示例 */
  NeedVariablesExample?: boolean;
  /** QueryId前缀搜索 */
  QueryIdPrefix?: string;
}

export interface GetQueryListResponse {
  queryList?: Array<QueryInfo>;
  count?: number;
}

export interface GetQueryResolverMapRequest {
  Query?: string;
}

export interface GetQueryResolverMapResp {
  FieldResolverList?: Array<string>;
  FieldResolverMap?: Record<string, string>;
}

export interface InfoDetail {
  infoKey: string;
  /** 用户组件子类型 */
  subType?: string;
  /** 1是 0否 */
  isList: number;
  /** 指令 */
  instruction?: string;
  /** 查询前缀 */
  queryPrefix?: string;
  /** 初始入参格式 */
  initFormat?: string;
  /** Action Key */
  key?: string;
  /** Action Key */
  aliasName?: string;
}

export interface QueryByApaasQueryRequest {
  QueryInfo?: QueryDTO;
  ParamMap?: Record<string, string>;
}

export interface QueryByApaasQueryResponse {
  Data?: string;
}

export interface QueryByInfoKeysRequest {
  /** 调用者psm */
  Psm: string;
  InfoKeys: Array<string>;
  Variables: string;
  UserInfo?: UserInfo;
  IsForceReturnLeftValue?: boolean;
  IsMustCompleteFieldNotNull?: boolean;
  ScenarioInfo?: ScenarioInfo;
  NewQueryID?: string;
  /** 上游by psm生成的unique id, for 埋点 */
  UniqueId?: string;
  UseNewCache?: boolean;
  /** 100以后国际化专用 */
  CacheConfig?: CacheConfig;
}

export interface QueryByInfoKeysResponse {
  Data?: string;
  Errors?: Array<string>;
  /** 100 以后国际化专用 */
  CacheProfilingInfo?: CacheProfilingInfo;
}

export interface QueryByInfoKeysV2Request {
  /** 调用者psm */
  Psm: string;
  InfoKeys: Array<string>;
  Variables?: Record<string, string>;
  SessionId?: string;
  UserId?: string;
  enableCache?: boolean;
  cacheTtl?: number;
  UserInfo?: UserInfo;
  IsForceReturnLeftValue?: boolean;
  ScenarioInfo?: ScenarioInfo;
  NewQueryID?: string;
  /** 上游by psm生成的unique id, for 埋点 */
  UniqueId?: string;
  UseNewCache?: boolean;
  /** 100以后国际化专用 */
  CacheConfig?: CacheConfig;
}

export interface QueryByInfoKeysV2Response {
  QueryResults?: Array<QueryResult>;
  Errors?: Array<string>;
  /** 100 以后国际化专用 */
  CacheProfilingInfo?: CacheProfilingInfo;
}

export interface QueryByQueryIDReq {
  QueryIDType: QueryIDType;
  QueryID: string;
  variables: string;
}

export interface QueryByQueryIDResp {
  Data?: string;
  Errors?: Array<string>;
  /** 正在使用的版本 */
  version: string;
}

export interface QueryDTO {
  SpaceId?: string;
  ReqConfig?: string;
  DataSourceConfig?: string;
  RespConfig?: string;
  ApaasMetaData?: string;
}

export interface QueryInfo {
  queryId: string;
  env: string;
  version: string;
  content?: string;
  variablesExample?: string;
  createAt?: Int64;
  psm?: string;
}

export interface QueryResult {
  InfoKey: string;
  InfoValue?: string;
  ValueSource?: InfoValueSource;
}

export interface Redis {
  ClusterName?: string;
}

/** 资源相关属性 */
export interface ResourceParams {
  /** 缩放宽  不传的话不生效 */
  PictureResizeWidth?: Int64;
  /** 缩放高  不传的话不生效 */
  PictureResizeHeight?: Int64;
}

export interface ScenarioDataRewriteRequest {
  ScenarioInfo?: ScenarioInfo;
  InfoKVs?: Record<string, string>;
}

export interface ScenarioDataRewriteResponse {}

export interface ScenarioInfo {
  /** 剧本信息
操作标识 */
  OpType: OpType;
  /** 唯一标识的key */
  ScenarioKey: string;
}

export interface UpdateQueryRequest {
  /** id */
  QueryId: string;
  /** 查询语句 */
  Content: string;
  /** 查询参数示例 */
  VariablesExample?: string;
  /** 生效环境，默认正式环境 */
  EnvList?: Array<string>;
  /** idl schema */
  SchemaId?: string;
}

export interface UpdateQueryResponse {
  version: string;
}

export interface UserInfo {
  UserId?: string;
  UserName?: string;
  Email?: string;
  AccessPartyId?: Int64;
  Resources?: Array<ResourceParams>;
  /** 加密入参 */
  EncryptionParams?: EncryptionParams;
  /** 版本id ，用来控制变量 */
  VersionID?: number;
}

export interface WebQueryRequest {
  RequestString: string;
  VariablesString?: string;
  OperationName?: string;
  UserInfo?: UserInfo;
}

export interface WebQueryResponse {
  Data?: string;
}

export default class DataMarketService<T> {
  private request: any = () => {
    throw new Error('DataMarketService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    request?<R>(
      params: {
        rpc: string;
        data?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=257342)
   *
   * Query
   */
  Query(req: DataQueryRequest, options?: T): Promise<DataQueryResponse> {
    const rpc = 'Query';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=257343)
   *
   * WebQuery
   */
  WebQuery(req: WebQueryRequest, options?: T): Promise<WebQueryResponse> {
    const rpc = 'WebQuery';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=374995)
   *
   * 获取query列表
   */
  GetQueryList(
    req: GetQueryListRequest,
    options?: T,
  ): Promise<GetQueryListResponse> {
    const rpc = 'GetQueryList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=374996)
   *
   * 获取query保存历史列表
   */
  GetQueryHistoryList(
    req: GetQueryHistoryListRequest,
    options?: T,
  ): Promise<GetQueryHistoryListResponse> {
    const rpc = 'GetQueryHistoryList';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=374997)
   *
   * 修改查询语句
   */
  UpdateQuery(
    req: UpdateQueryRequest,
    options?: T,
  ): Promise<UpdateQueryResponse> {
    const rpc = 'UpdateQuery';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=374998)
   *
   * 通过queryId获取配置
   */
  GetQueryInfo(
    req: GetQueryInfoRequest,
    options?: T,
  ): Promise<GetQueryInfoResponse> {
    const rpc = 'GetQueryInfo';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=374999)
   *
   * 添加查询语句
   */
  AddQuery(req: AddQueryRequest, options?: T): Promise<AddQueryResponse> {
    const rpc = 'AddQuery';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=375000)
   *
   * 使指定版本生效
   */
  EnableQuery(
    req: EnableQueryRequest,
    options?: T,
  ): Promise<EnableQueryResponse> {
    const rpc = 'EnableQuery';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=570535)
   *
   * 通过info keys请求下游
   */
  QueryByInfoKeys(
    req: QueryByInfoKeysRequest,
    options?: T,
  ): Promise<QueryByInfoKeysResponse> {
    const rpc = 'QueryByInfoKeys';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=658096)
   *
   * 通过info keys请求下游
   */
  QueryByInfoKeysV2(
    req: QueryByInfoKeysV2Request,
    options?: T,
  ): Promise<QueryByInfoKeysV2Response> {
    const rpc = 'QueryByInfoKeysV2';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=808050)
   *
   * 批量校验InfoKey是否合法
   */
  CheckInfoKeysIsValid(
    req: CheckInfoKeysIsValidRequest,
    options?: T,
  ): Promise<CheckInfoKeysIsValidResponse> {
    const rpc = 'CheckInfoKeysIsValid';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=903247)
   *
   * 根据infoKey生成Query
   */
  GenQueryByInfoKeys(
    req: GenQueryByInfoKeysRequest,
    options?: T,
  ): Promise<GenQueryByInfoKeysResponse> {
    const rpc = 'GenQueryByInfoKeys';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=937782)
   *
   * 获取Query关联的ResolverMap
   */
  GetQueryResolverMap(
    req?: GetQueryResolverMapRequest,
    options?: T,
  ): Promise<GetQueryResolverMapResp> {
    const rpc = 'GetQueryResolverMap';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=956621)
   *
   * 练线锦囊设置缓存的infokv
   */
  ScenarioDataRewrite(
    req?: ScenarioDataRewriteRequest,
    options?: T,
  ): Promise<ScenarioDataRewriteResponse> {
    const rpc = 'ScenarioDataRewrite';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=1288168)
   *
   * 通过queryid 或者别名请求
   */
  QueryByQueryID(
    req: QueryByQueryIDReq,
    options?: T,
  ): Promise<QueryByQueryIDResp> {
    const rpc = 'QueryByQueryID';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2186291)
   *
   * 通过Apaas完整query查询数据
   */
  QueryByApaasQuery(
    req?: QueryByApaasQueryRequest,
    options?: T,
  ): Promise<QueryByApaasQueryResponse> {
    const rpc = 'QueryByApaasQuery';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2233278)
   *
   * APaaS Query查询
   */
  APaaSQueryExecute(
    req: APaaSQueryExecuteRequest,
    options?: T,
  ): Promise<APaaSQueryExecuteResponse> {
    const rpc = 'APaaSQueryExecute';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2452136)
   *
   * APaaS字段 查询
   */
  CommonAPaaSQuery(
    req?: CommonAPaaSQueryRequest,
    options?: T,
  ): Promise<CommonAPaaSQueryResponse> {
    const rpc = 'CommonAPaaSQuery';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2593766)
   *
   * APaaS 动作执行
   */
  CommonAPaaSAction(
    req: CommonAPaaSActionRequest,
    options?: T,
  ): Promise<CommonAPaaSActionResponse> {
    const rpc = 'CommonAPaaSAction';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2593767)
   *
   * APaaS 动作测试
   */
  APaaSActionTest(
    req?: APaaSActionTestRequest,
    options?: T,
  ): Promise<APaaSActionTestResponse> {
    const rpc = 'APaaSActionTest';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2630807)
   *
   * 批量APaaS查询(for jiuquan)
   */
  BatchCommonAPaaSQuery(
    req?: BatchCommonAPaaSQueryRequest,
    options?: T,
  ): Promise<BatchCommonAPaaSQueryResponse> {
    const rpc = 'BatchCommonAPaaSQuery';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2702733)
   *
   * APaaS 流程测试
   */
  APaaSWorkflowTest(
    req?: APaaSWorkflowTestRequest,
    options?: T,
  ): Promise<APaaSWorkflowTestResponse> {
    const rpc = 'APaaSWorkflowTest';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=2906887)
   *
   * APaaS 动态枚举查询
   */
  APaaSDynamicEnumExecute(
    req: APaaSDynamicEnumExecuteRequest,
    options?: T,
  ): Promise<APaaSDynamicEnumExecuteResponse> {
    const rpc = 'APaaSDynamicEnumExecute';
    return this.request({ rpc, data: req }, options);
  }

  /**
   * [jump to BAM](https://cloud.bytedance.net/bam/rd/ies.kefu.datamarket/api_doc/show_doc?version=1.0.93&endpoint_id=3035682)
   *
   * 对外暴露泛化调用接口
   */
  GenericRPCCall(
    req: GenericRPCCallRequest,
    options?: T,
  ): Promise<GenericRPCCallResponse> {
    const rpc = 'GenericRPCCall';
    return this.request({ rpc, data: req }, options);
  }
}
/* eslint-enable */
