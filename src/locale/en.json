{"api_back": "Return to API list", "api_create": "New API", "api_desc": "API is the underlying interface that info depends on. This page can view, add, edit, enable/disable, etc. the API", "api_disable_fail_tips": "The API has been called by INFO, please disable the relevant INFO before disabling the API", "api_enable_fail_tips": "Failed to enable API", "api_enable_tips": "Once enabled, the API will be called by each application channel", "api_title": "API list", "common_cancel": "Cancel", "common_choose_api": "Please select API Access", "common_confirm": "Confirm", "common_confirm_quit": "Confirm exiting \"{title}\"?", "common_create": "New", "common_create_fail": "New failed", "common_create_success": "New successfully", "common_delete": "Delete", "common_delete_by_name": "Are you sure you want to delete \"{name}\"?", "common_delete_fail": "Delete failed", "common_delete_fail_by_name": "\"{Name}\" delete failed", "common_delete_success": "Deleted successfully", "common_delete_success_by_name": "\"{name}\" deleted successfully", "common_delete_tips": "Are you sure you want to delete \"{title}\"?", "common_disable": "Disable", "common_disable_error_title": "Can't disable \"{title}\"?", "common_divert": "<PERSON><PERSON>", "common_edit": "Edit", "common_edit_name": "Edit \"{name}\"", "common_empty_data": "No data yet", "common_enable": "Enable", "common_enable_error_title": "Can't enable \"{title}\"?", "common_enable_fail_tips": "\"{Name}\" {type} failed", "common_enable_name": "Enable \"{name}\"?", "common_enable_success_tips": "\"{Name}\" {type} succeeded", "common_fail": "Fail", "common_fetch_more_operation_record": "Show more", "common_fetch_operation_record_fail": "Failed to get operation record", "common_form_check_error": "Form validation failed", "common_form_loading": "Verifying data, please wait...", "common_form_save_success": "Form saved successfully", "common_form_trans_fail": "Form flow failed", "common_get_data_auto": "Automatically get test data", "common_get_data_fail_tips": "Randomly obtain test data from the log system, and there are cases where it cannot be obtained", "common_getting_data": "Fetching data...", "common_input_placeholder_by_name": "Please enter {name}", "common_list_empty": "No search results found", "common_no_form_data": "Failed to get data", "common_no_form_data_tips": "Please check if the ID is correct", "common_no_perms": "No permission", "common_no_perms_tips": "The user information is not queried or the user has been disabled, please refresh and try again", "common_ok": "Okay", "common_operate_time": "Operation time", "common_operation_record": "Operation record", "common_operation_record_empty": "No operation record", "common_page_error": "Page display failed, please", "common_page_error_operation": "Refresh retry", "common_placeholder_enter_text": "Please enter text", "common_placeholder_search_people": "Please enter a name", "common_placeholder_search_select": "Search filter", "common_placeholder_search_time": "Search time", "common_prompt_message": "Confirm to exit \"{title}\"? \n | Once you exit, unsaved operations cannot be resumed, please proceed with caution", "common_quit": "Exit", "common_quit_tips": "Once you exit, unsaved operations cannot be recovered, please proceed with caution", "common_release": "Release", "common_run_data": "Operational data", "common_save": "Save", "common_save_fail": "Failed to save", "common_save_success": "Save successfully", "common_status_disable": "Disabled", "common_status_enable": "Enabled", "common_status_inconfig": "Configuring", "common_success": "Success", "common_test_fail": "Data test error", "common_test_success": "Data test successful", "common_trans_check_fail": "Translation check status failed", "common_trans_placeholder": "Please enter your name", "common_trans_rulesMsg": "Name Required", "common_trans_success": "Transfer and save successfully", "common_trans_tips": "Remarks: If the interface information is complete and there is no specific transfer person, it can be transferred to info RD [Kong Wenbin]", "common_translation": "Translation", "common_update": "Update", "info_back": "Return to INFO list", "info_create": "New INFO", "info_desc": "Info is a system variable accessed by <PERSON> configuration desk, Jiuquan configuration desk, work order configuration desk, etc. This page can view, add, edit, enable/disable info, etc", "info_disable_error_tips": "The INFO has been called in the online scene, please offline the relevant caller before disabling it", "info_enable_error_tips": "The API corresponding to this INFO has been disabled, please enable the corresponding API first", "info_enable_tips": "When enabled, the INFO will be called by each application channel", "info_title": "INFO list"}