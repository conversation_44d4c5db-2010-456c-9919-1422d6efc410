{"api_back": "返回 API 列表", "api_create": "新建 API", "api_desc": "API 是info依赖的底层接口，该页面可对API进行查看、新增、编辑、启用/禁用等操作", "api_disable_fail_tips": "该 API 已被 INFO 调用，请先禁用相关 INFO 后再禁用该 API", "api_enable_fail_tips": "启用API失败", "api_enable_tips": "启用后，该 API 将会被各应用渠道调用", "api_title": "API 列表", "common_cancel": "取消", "common_choose_api": "请选择API接入", "common_confirm": "确认", "common_confirm_quit": "确认退出「{title}」？", "common_create": "新建", "common_create_fail": "新建失败", "common_create_success": "新建成功", "common_delete": "删除", "common_delete_by_name": "确认删除「{name}」?", "common_delete_fail": "删除失败", "common_delete_fail_by_name": "「{name}」删除失败", "common_delete_success": "删除成功", "common_delete_success_by_name": "「{name}」删除成功", "common_delete_tips": "确认删除「{title}」?", "common_disable": "禁用", "common_disable_error_title": "无法禁用「{title}」？", "common_divert": "流转", "common_edit": "编辑", "common_edit_name": "编辑「{name}」", "common_empty_data": "暂无数据", "common_enable": "启用", "common_enable_error_title": "无法启用「{title}」？", "common_enable_fail_tips": "「{name}」{type}失败", "common_enable_name": "启用「{name}」？", "common_enable_success_tips": "「{name}」{type}成功", "common_fail": "失败", "common_fetch_more_operation_record": "显示更多", "common_fetch_operation_record_fail": "获取操作记录失败", "common_form_check_error": "表单校验不通过", "common_form_loading": "校验数据中，请稍后...", "common_form_save_success": "表单保存成功", "common_form_trans_fail": "表单流转失败", "common_get_data_auto": "自动获取测试数据", "common_get_data_fail_tips": "从日志系统随机获取测试数据，存在获取不到的情况", "common_getting_data": "正在获取数据中…", "common_input_placeholder_by_name": "请输入{name}", "common_list_empty": "未找到搜索结果", "common_no_form_data": "获取数据失败", "common_no_form_data_tips": "请检查ID是否正确", "common_no_perms": "无权限", "common_no_perms_tips": "未查询到用户信息或用户已被禁用，请刷新重试", "common_ok": "好的", "common_operate_time": "操作时间", "common_operation_record": "操作记录", "common_operation_record_empty": "暂无操作记录", "common_page_error": "页面显示失败，请", "common_page_error_operation": "刷新重试", "common_placeholder_enter_text": "请输入文字", "common_placeholder_search_people": "请输入人名", "common_placeholder_search_select": "搜索筛选项", "common_placeholder_search_time": "搜索时间", "common_prompt_message": "确认退出「{title}」？\n            |一旦退出，未保存的操作将无法恢复，请谨慎操作", "common_quit": "退出", "common_quit_tips": "一旦退出，未保存的操作将无法恢复，请谨慎操作", "common_release": "发布", "common_run_data": "运行数据", "common_save": "保存", "common_save_fail": "保存失败", "common_save_success": "保存成功", "common_status_disable": "已禁用", "common_status_enable": "已启用", "common_status_inconfig": "配置中", "common_success": "成功", "common_test_fail": "数据测试错误", "common_test_success": "数据测试成功", "common_trans_check_fail": "翻译校验状态失败", "common_trans_placeholder": "请输入姓名", "common_trans_rulesMsg": "姓名必填", "common_trans_success": "流转并保存成功", "common_trans_tips": "备注：若接口信息完整且无特定流转人，可流转给info RD【孔文斌】", "common_translation": "翻译", "common_update": "更新", "info_back": "返回 INFO 列表", "info_create": "新建 INFO", "info_desc": "info 是锦囊配置台、酒泉配置台、工单配置台等接入的系统变量，该页面可对info进行查看、新增、编辑、启用/禁用等操作", "info_disable_error_tips": "该 INFO 已在线上场景调用，请先下线相关调用方后再禁用", "info_enable_error_tips": "该INFO对应的API已被禁用，请先启用对应API", "info_enable_tips": "启用后，该 INFO 将会被各应用渠道调用", "info_title": "INFO 列表"}