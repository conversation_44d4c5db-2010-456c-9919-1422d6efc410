/**
 * 查看操作列表页面，主要展示操作列表信息，可以进行时间搜索。
 */
import { FC, useEffect } from 'react';
import { infoServiceClient, Target } from '@http_idl/infoService';
import { Toast } from '@hi-design/ui';
import OptionsFilterGroup from './components/OptionsFilterGroup';
import OptionsRecordsList from './components/OptionsRecordsList';
import useOptionsRecords from '@hooks/useOptionsRecords';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';

interface IOptionsRecords {
  target: Target;
  id: string;
}

interface IfetchDetailParams {
  isFilter?: boolean;
  filterInfo?: {
    startTime?: string;
    endTime?: string;
  };
}

const OptionsRecords: FC<IOptionsRecords> = props => {
  const { target, id } = props;

  const {
    scrollRef,
    queryTime,
    recordList,
    loading,
    listLoading,
    hasMore,
    count,
    filterInfo,
    setRecordList,
    setLoading,
    setListLoading,
    setHasMore,
    setCount,
    setFilterInfo,
  } = useOptionsRecords();

  const fetchDetail = async (
    params?: IfetchDetailParams,
    filterCount?: number,
  ): Promise<void> => {
    const { isFilter } = params || {};
    if (isFilter) {
      setListLoading(true);
    } else {
      setLoading(true);
    }
    try {
      const paramsFilterInfo = isFilter ? params?.filterInfo || {} : filterInfo;
      const records = await infoServiceClient.GetOperateRecordList({
        target,
        id,
        queryTime,
        pageSize: 15,
        pageNum: isFilter ? filterCount : count,
        ...paramsFilterInfo,
      });
      if (records?.BaseResp?.StatusCode !== 0) {
        Toast.error(
          records?.BaseResp?.StatusMessage ||
            COMMON_FE_TEXT_MAP.Fetch_Operation_Record_Error,
        );
      }
      if (!isFilter) {
        const newDataSource = [
          ...recordList,
          ...(records?.operateRecordList || []),
        ];
        setRecordList(newDataSource);
        setHasMore(records?.pageCount > count);
        setCount(item => item + 1);
      } else {
        setRecordList(records?.operateRecordList || []);
        scrollRef.current.scrollTo(0, 0);
        setHasMore(records?.pageCount > filterCount);
        setCount(filterCount + 1);
      }
    } catch (err) {
      setHasMore(false);
    }
    setListLoading(false);
    setLoading(false);
  };

  useEffect(() => {
    fetchDetail();
  }, []);

  const handleChangeFilter = (data: any): void => {
    const params =
      data.length > 0
        ? {
            stratTime: String(data[0]?.getTime()),
            endTime: String(data[1]?.getTime()),
          }
        : {};
    setFilterInfo(params);
    fetchDetail(
      {
        isFilter: true,
        filterInfo: params,
      },
      1,
    );
  };

  return (
    <div>
      <OptionsFilterGroup onChange={handleChangeFilter} />
      <OptionsRecordsList
        data={recordList || []}
        fetchData={fetchDetail}
        loading={loading}
        listLoading={listLoading}
        hasMore={hasMore}
        count={count}
        scrollRef={scrollRef}
      />
    </div>
  );
};

export default OptionsRecords;
