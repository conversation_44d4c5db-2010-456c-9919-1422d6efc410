import { FC } from 'react';
import { Timeline } from '@hi-design/ui';
import Styles from './index.module.scss';
import { OperateRecord } from '@http_idl/infoService';

interface IOptionsRecords {
  data: OperateRecord;
  index: number;
}

interface IExtraTimeLine {
  data: Array<string>;
}

const ExtraTimeLine = (props: IExtraTimeLine): any => {
  const { data = [] } = props;
  return (
    <>
      {data.map((item, index) => (
        <div className={Styles.extraTimeLineItemContainer} key={index}>
          {item}
        </div>
      ))}
    </>
  );
};

const OptionsRecordsItem: FC<IOptionsRecords> = props => {
  const { data, index } = props;
  const { operateTime, operateUserMail, operateDesc, operateDetail } =
    data || {};
  return (
    <div className={Styles.optionsRecordItemContainer}>
      <Timeline.Item
        type={index === 0 ? 'ongoing' : 'default'}
        time={operateTime}
        extra={<ExtraTimeLine data={operateDetail || []} />}
      >
        <span className={Styles.operateUser}>{operateUserMail}</span>
        {operateDesc && (
          <span className={Styles.operateTitle}>{operateDesc}</span>
        )}
      </Timeline.Item>
    </div>
  );
};

export default OptionsRecordsItem;
