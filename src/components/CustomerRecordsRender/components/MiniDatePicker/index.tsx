import { FC, useCallback, useMemo, useState } from 'react';
import { isFunction } from 'lodash-es';
import cls from 'classnames';
import { DatePicker } from '@hi-design/ui';
import { formatDataListToString } from '@common/utils/format';
import { IconClear, IconDown } from '@components/IconOperate';
import Styles from './index.module.scss';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';

interface IMiniDatePicker {
  defaultValues?: Array<Date>;
  onChange: (value: any) => void;
  title?: string;
}

const MiniDatePicker: FC<IMiniDatePicker> = props => {
  const {
    onChange,
    title = COMMON_FE_TEXT_MAP.Operate_Time,
    defaultValues = [],
  } = props;
  const [localValues, setLocalValues] = useState(defaultValues);
  const [isExpanded, setIsExpanded] = useState(false);
  const isEmpty = useMemo(() => localValues.length <= 0, [localValues.length]);

  const handleConditionChange = (data: any): void => {
    if (isFunction(onChange)) {
      setLocalValues(data);
      onChange(data);
    }
  };
  const handleOnClickTrigger = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  return (
    <DatePicker
      type="dateTimeRange"
      onChange={handleConditionChange}
      autoAdjustOverflow={false}
      density="compact"
      open={isExpanded}
      value={localValues}
      onOpenChange={(status): void => {
        setIsExpanded(status);
      }}
      triggerRender={(): React.ReactNode => (
        <div
          className={cls({
            [Styles.button]: true,
            [Styles.buttonChoosen]: isExpanded || !isEmpty,
          })}
          onClick={handleOnClickTrigger}
        >
          <div className={Styles.buttonTitle}>{title}</div>
          {!isEmpty && (
            <div className={Styles.buttonContent}>
              {formatDataListToString(localValues)}
            </div>
          )}
          {!isEmpty ? (
            <IconClear clickCancel={(): void => handleConditionChange([])} />
          ) : (
            <IconDown isExpanded={isExpanded} />
          )}
        </div>
      )}
    />
  );
};

export default MiniDatePicker;
