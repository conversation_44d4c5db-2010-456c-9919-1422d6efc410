import { FC, ReactNode } from 'react';
import { Button, List, Spin, Empty } from '@hi-design/ui';
import {
  IllustrationIdle,
  IllustrationIdleDark,
} from '@hi-design/ui-illustrations';
import InfiniteScroll from 'react-infinite-scroller';
import Styles from './index.module.scss';
import OptionsRecordsItem from '../OptionsRecordsItem';

import { isEmpty } from 'lodash-es';
import { OperateRecord } from '@http_idl/infoService';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';

interface IOptionsRecordsList {
  data: Array<OperateRecord>;
  fetchData: () => void;
  loading: boolean;
  listLoading: boolean;
  hasMore: boolean;
  count: number;
  scrollRef?: any;
}

const renderLoadMore = (
  loading,
  hasMore,
  showLoadMore,
  fetchData,
): ReactNode => (
  <div>
    {!loading && hasMore && showLoadMore ? (
      <div
        style={{
          textAlign: 'center',
          marginTop: 12,
          height: 32,
          lineHeight: '32px',
        }}
      >
        <Button onClick={fetchData}>
          {COMMON_FE_TEXT_MAP.Fetch_More_Operation_Record}
        </Button>
      </div>
    ) : null}
  </div>
);

const renderEmptyContent = (): ReactNode => (
  <Empty
    image={<IllustrationIdle style={{ width: 150, height: 150 }} />}
    darkModeImage={<IllustrationIdleDark style={{ width: 150, height: 150 }} />}
    description={COMMON_FE_TEXT_MAP.Operation_Record_Empty}
  >
    <></>
  </Empty>
);

const OptionsRecordsList: FC<IOptionsRecordsList> = props => {
  const { data, loading, listLoading, hasMore, scrollRef, fetchData } = props;
  return (
    <div ref={scrollRef} className={Styles['options-light-scrollbar']}>
      <InfiniteScroll
        initialLoad={false}
        pageStart={0}
        threshold={20}
        loadMore={fetchData}
        hasMore={!loading && hasMore}
        useWindow={false}
      >
        <List
          loading={listLoading}
          loadMore={renderLoadMore(loading, hasMore, false, fetchData)}
          dataSource={data}
          renderItem={(item, index): ReactNode => (
            <OptionsRecordsItem data={item} index={index} key={index} />
          )}
        />
        {!loading && isEmpty(data) && renderEmptyContent()}
        {loading && hasMore && (
          <div style={{ textAlign: 'center' }}>
            <Spin />
          </div>
        )}
      </InfiniteScroll>
    </div>
  );
};

export default OptionsRecordsList;
