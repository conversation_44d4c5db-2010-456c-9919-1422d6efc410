import { FC } from 'react';
import { Empty, Typography } from '@hi-design/ui';
import {
  IllustrationFailureDark,
  IllustrationFailure,
} from '@hi-design/ui-illustrations';
import styles from './index.module.scss';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
const PageEmpty: FC = () => {
  // 刷新页面
  const handleRefresh = (): void => {
    window.location.reload();
  };
  return (
    <Empty
      image={<IllustrationFailure style={{ width: 150, height: 150 }} />}
      darkModeImage={
        <IllustrationFailureDark style={{ width: 150, height: 150 }} />
      }
      className={styles.empty}
      description={
        <span>
          <Typography.Text>{COMMON_FE_TEXT_MAP.Page_Error}</Typography.Text>
          <Typography.Text link onClick={handleRefresh}>
            {COMMON_FE_TEXT_MAP.Page_Error_Operation}
          </Typography.Text>
        </span>
      }
    />
  );
};
export default PageEmpty;
