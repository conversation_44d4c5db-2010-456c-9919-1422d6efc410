/*
 * @Description: 筛选项组件
 * @Props: list: 从接口处获取的筛选项的shcema
 * @Props: onFilter: 处理筛选项变化的函数
 */
import { FC, useMemo } from 'react';
import { Filter } from '@hi-design/ui';
import transferFilter from '@common/utils/transferFilter';
import { ManageType } from '@common/utils/starlingKey';

interface FilterProps {
  list: string;
  manageType: ManageType;
  onFilter: (newFilter: any) => void;
}

const ListFilter: FC<FilterProps> = ({ list, manageType, onFilter }) => {
  const listItems = useMemo(() => {
    if (!list) {
      return [];
    }
    const filters = transferFilter(list, manageType);
    return filters;
  }, [list, manageType]);
  return (
    <Filter onValuesChange={(newFilters): void => onFilter(newFilters)}>
      {listItems}
    </Filter>
  );
};

export default ListFilter;
