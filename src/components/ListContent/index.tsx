// info页面的筛选项和列表
import { FC, useMemo } from 'react';
import CustomerRecordsModal from '@components/CustomerRecordsModal';
import cn from 'classnames';
import Filter from '@components/Filter';
import Table from '@components/Table';
import { PermissionPoint, Target } from '@http_idl/infoService';
import { Button, Space, Tooltip } from '@hi-design/ui';
import { useStores } from '@stores/index';
import { observer } from 'mobx-react';
import PageEmpty from '@components/PageEmpty';
import {
  API_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
  INFO_FE_TEXT_MAP,
} from '@common/constants/I18nTextMap';
import { ManageType } from '@common/utils/starlingKey';
import { useListState } from '@hooks/useListState';
import styles from './index.module.scss';
import { IconBeaker, IconPlusCircle } from '@hi-design/ui-icons';

interface IListContent {
  target: Target;
}
const ListContent: FC<IListContent> = props => {
  const { target } = props || {};

  const { staff, unified, loading } = useStores();

  // 获得人员权限
  const { staffInfoType, staffAPIType } = staff || {};
  // 获取人员信息
  const { agent } = unified;
  // 页面loading
  const { setPageLoading, pageLoading } = loading || {};

  const permissionPoint = useMemo(() => {
    if (target === Target.Api) {
      return staffAPIType;
    }
    if (target === Target.Info) {
      return staffInfoType;
    }
    return PermissionPoint.Operate;
  }, [target, staffAPIType, staffInfoType]);

  const manageType = useMemo(() => {
    if (target === Target.Api) {
      return ManageType.API;
    }
    if (target === Target.Info) {
      return ManageType.INFO;
    }
    return ManageType.COMMON;
  }, [target]);

  const {
    dataId,
    pageSchema,
    listData,
    isPageEmpty,
    pagination,
    selectSchema,
    tableLoading,
    recordsVisible,
    operateColumnsMap,
    tableWidth,
    handleFilter,
    onJumpFormDetail,
    onJumpDebug,
    onRecordsCancel,
  } = useListState({
    target,
    permissionPoint,
    agent,
    setPageLoading,
  });

  const CreateInfo = useMemo(() => {
    const CreateTite =
      target === Target.Info ? INFO_FE_TEXT_MAP.Create : API_FE_TEXT_MAP.Create;
    const isShowCreate =
      permissionPoint === PermissionPoint.Development && selectSchema !== '';
    return {
      show: isShowCreate,
      title: CreateTite,
    };
  }, [target, permissionPoint, selectSchema]);

  return (
    <>
      <div className={cn(styles.firstLine, 'filterContainer')}>
        <Filter
          manageType={manageType}
          list={selectSchema}
          onFilter={handleFilter}
        />
        <Space>
          {target === Target.Info && (
            <Tooltip content={INFO_FE_TEXT_MAP.DEBUG_TIP}>
              <Button
                icon={<IconBeaker />}
                onClick={(): void => onJumpDebug({})}
              >
                {INFO_DEBUG_TEXT_MAP.LOG_ANALYSIS}
              </Button>
            </Tooltip>
          )}
          {CreateInfo?.show ? (
            <Button
              theme="solid"
              icon={<IconPlusCircle />}
              onClick={(): void => onJumpFormDetail({})}
            >
              {CreateInfo?.title}
            </Button>
          ) : null}
        </Space>
      </div>
      {!isPageEmpty ? (
        <div className={styles.TableWrapper}>
          <Table
            data={{
              columns: pageSchema,
              operateColumns: operateColumnsMap,
              listData,
              pagination,
            }}
            loading={!pageLoading && tableLoading}
            pageLoading={pageLoading}
            manageType={manageType}
            tableWidth={tableWidth}
          />
        </div>
      ) : (
        <PageEmpty />
      )}
      <CustomerRecordsModal
        visible={recordsVisible}
        target={target}
        id={dataId}
        onCancel={onRecordsCancel}
      />
    </>
  );
};

export default observer(ListContent);
