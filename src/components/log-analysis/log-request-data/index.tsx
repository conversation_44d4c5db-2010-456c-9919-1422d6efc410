import { TabPane, Tabs, Typography } from '@hi-design/ui';
import { FC } from 'react';
import styles from './index.module.scss';
import { RequestParams } from '../request-params';
import { InfoValueTable } from '../info-value-table';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { NoData } from '../no-data';
export interface LogRequest {
  key: string;
  tabDesc: string;
  requestParams: {
    title: string;
    value: string;
  }[];
  infoKeyResults: info_service.InfoKeyResult[];
  infoKeyDiff: string[];
}

export interface ILogRequestDataProps {
  requestData: LogRequest[];
  activeInfoKey?: string;
}

export const LogRequestData: FC<ILogRequestDataProps> = props => {
  const { requestData, activeInfoKey } = props;
  if (!requestData?.length) {
    return <NoData />;
  }
  return (
    <Tabs collapsible>
      {requestData.map((item, idx) => (
        <TabPane
          key={item.key}
          itemKey={item.key}
          tab={
            <div>
              <Typography.Text>
                {INFO_DEBUG_TEXT_MAP.REQUEST_NUMBER(idx + 1)}
              </Typography.Text>
              <div>
                <Typography.Text type="secondary" size="small">
                  {item.tabDesc}
                </Typography.Text>
              </div>
            </div>
          }
        >
          <div className={styles.tabPaneContainer}>
            <section>
              <Typography.Text strong className={styles.header}>
                {INFO_DEBUG_TEXT_MAP.REQUEST_PARAMS}
              </Typography.Text>
              <RequestParams
                data={item.requestParams?.map(item => ({
                  ...item,
                  key: item.title,
                }))}
                className={styles.descriptions}
              />
            </section>
            <section>
              <Typography.Text strong className={styles.header}>
                {INFO_DEBUG_TEXT_MAP.INFO_DATA_RETURN}
              </Typography.Text>
              <InfoValueTable
                infoKeyDiff={item.infoKeyDiff}
                infoKeyResults={item.infoKeyResults}
                activeInfoKey={activeInfoKey}
              />
            </section>
          </div>
        </TabPane>
      ))}
    </Tabs>
  );
};
