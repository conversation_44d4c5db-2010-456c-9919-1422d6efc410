.container {
  .panel {
    .headerContainer {
      display: flex;
      align-items: center;

      .left {
        margin-right: 8px;

        .seqNumber {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 28px; /* Equal width and height */
          height: 28px;
          border-radius: 50%;
          background-color: rgba(var(--semi-grey-1), 1);
          color: rgba(var(--semi-grey-5), 1);
          font-weight: 700;

          &.success {
            background-color: var(--semi-color-success);
            color: var(--semi-color-bg-0);
          }

          &.fail {
            background-color: var(--semi-color-danger);
            color: var(--semi-color-bg-0);
          }
        }
      }

      .right {
        .headerText {
        }
        .headerDesc {
          display: block;
        }
      }
    }

    .panelContent {
      .banner {
        margin: 12px 0;

        .bannerContent {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }

      .errorDetails {
        border: 1px solid var(--semi-color-border);
        padding: 8px 12px;
        border-radius: 4px;
        background-color: rgba(var(--semi-grey-8), 0.02);

        .errorDetailsTitle {
          display: block;
          margin-bottom: 4px;
        }

        .emptyErrorDetails {
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .errorList {
          min-height: 70px;

          .errorItem {
            margin-bottom: 8px;

            .errorItemHeader {
              display: block;
              margin-bottom: 2px;
              padding-bottom: 2px;
              border-bottom: 1px solid var(--semi-color-border);
            }
          }

          ul {
            list-style: none;
            padding: 0;
            margin: 0;
            & > li {
              margin: 0;
            }

            .errorText {
              word-break: break-all;
              margin-bottom: 4px;
            }
          }
        }
      }
    }
  }
}
