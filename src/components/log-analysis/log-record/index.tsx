import { FC, useState } from 'react';
import styles from './index.module.scss';
import { REQ_EXEC_NODE_MAP, ReqExecNodeStatus } from '@/common/constants/info';
import { Banner, Collapse, Collapsible, Typography } from '@hi-design/ui';
import classNames from 'classnames';
import { IconChevronDown, IconChevronUp } from '@hi-design/ui-icons';
import { LogRenderer } from '../log-renderer';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { isEmpty } from 'lodash-es';
import {
  COMMON_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
} from '@/common/constants/I18nTextMap';
import AvatarLark from '@ies/semi-react-user-card';

export interface ILogRecordProps {
  errorList: Partial<Record<info_service.ErrorParty, info_service.ErrorInfo>>;
}

export const LogRecord: FC<ILogRecordProps> = props => {
  const { errorList } = props;

  const [expandErrorItem, setExpandErrorItem] = useState<boolean[]>(
    Object.entries(errorList).map(() => false),
  );

  const executionProgressArray = Object.entries(errorList).map(
    ([reqExecNode, progressValue]) => ({
      reqExecNode: Number(reqExecNode) as info_service.ErrorParty,
      ...progressValue,
    }),
  );

  const renderHeader = (
    reqExecNode: info_service.ErrorParty,
    status: ReqExecNodeStatus,
  ) => (
    <div className={styles.headerContainer}>
      <div className={styles.left}>
        <Typography.Text
          className={classNames(styles.seqNumber, {
            [styles.success]: status === ReqExecNodeStatus.Success,
            [styles.fail]: status === ReqExecNodeStatus.Fail,
          })}
        >
          {reqExecNode + 1}
        </Typography.Text>
      </div>
      <div className={styles.right}>
        <Typography.Text strong className={styles.headerText}>
          {REQ_EXEC_NODE_MAP[reqExecNode]}
        </Typography.Text>
        <Typography.Text
          type="tertiary"
          size="small"
          className={styles.headerDesc}
        >
          {status === ReqExecNodeStatus.Fail
            ? INFO_DEBUG_TEXT_MAP.EXECUTION_ERROR
            : status === ReqExecNodeStatus.Success
              ? INFO_DEBUG_TEXT_MAP.EXECUTION_SUCCESS
              : INFO_DEBUG_TEXT_MAP.EXECUTION_NO_RESULT}
        </Typography.Text>
      </div>
    </div>
  );

  const renderPanelContent = (
    item: {
      status: ReqExecNodeStatus;
      errorDetails: Record<string, string[]>;
      logs: info_service.LogEntry[];
      reqExecNode: info_service.ErrorParty;
    },
    index: number,
  ) => {
    const { reqExecNode, logs, errorDetails } = item;

    return (
      <div className={styles.panelContent}>
        {reqExecNode === info_service.ErrorParty.DATAMARKET ||
        reqExecNode === info_service.ErrorParty.DMSCRIPT ? (
          <Banner
            className={styles.banner}
            fullMode={false}
            type="warning"
            bordered
            closeIcon={null}
            description={
              <div className={styles.bannerContent}>
                {INFO_DEBUG_TEXT_MAP.CONTACT_DEVELOPER}
                <AvatarLark email="<EMAIL>" mode="simple" />
              </div>
            }
          />
        ) : null}
        <div className={styles.errorDetails}>
          <Typography.Text className={styles.errorDetailsTitle}>
            {INFO_DEBUG_TEXT_MAP.ERROR_DETAILS}
          </Typography.Text>
          {isEmpty(errorDetails) ? (
            <div className={styles.emptyErrorDetails}>
              <Typography.Text type="tertiary">
                {INFO_DEBUG_TEXT_MAP.NO_ERROR_DETAILS}
              </Typography.Text>
            </div>
          ) : (
            <div>
              <Collapsible
                isOpen={expandErrorItem[index] || false}
                collapseHeight={60}
              >
                <div className={styles.errorList}>
                  {Object.entries(errorDetails)
                    .sort((a, b) => Number(a[0]) - Number(b[0])) // 用key (spanID) 排序
                    .map(([key, value]) => (
                      <div className={styles.errorItem} key={key}>
                        <Typography.Text className={styles.errorItemHeader}>
                          {INFO_DEBUG_TEXT_MAP.REQ_ID(key)}
                        </Typography.Text>
                        <ul>
                          {value.map((error, idx) => (
                            <li key={`${key}-${error}`}>
                              <Typography.Text
                                className={styles.errorText}
                                ellipsis={{
                                  rows: 3,
                                  expandable: true,
                                  collapsible: true,
                                }}
                                type="secondary"
                              >
                                Error {idx + 1}: {error}
                              </Typography.Text>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                </div>
              </Collapsible>
              <Typography.Text
                className={styles.expandBtn}
                link
                size="small"
                icon={
                  expandErrorItem[index] ? (
                    <IconChevronUp />
                  ) : (
                    <IconChevronDown />
                  )
                }
                onClick={() => {
                  setExpandErrorItem(prev => {
                    const newExpandErrorItem = [...prev];
                    newExpandErrorItem[index] = !newExpandErrorItem[index];
                    return newExpandErrorItem;
                  });
                }}
              >
                {expandErrorItem[index]
                  ? `${COMMON_FE_TEXT_MAP.Collapse}${INFO_DEBUG_TEXT_MAP.ERROR_DETAILS}`
                  : `${COMMON_FE_TEXT_MAP.Expand}${INFO_DEBUG_TEXT_MAP.ERROR_DETAILS}`}
              </Typography.Text>
            </div>
          )}
        </div>
        <LogRenderer logs={logs} />
      </div>
    );
  };

  return (
    <Collapse className={styles.container} expandIconPosition="left">
      {executionProgressArray?.map((item, index) => {
        const hasContent =
          !isEmpty(item.errorDetails) ||
          item.logs.length > 0 ||
          item.reqExecNode === info_service.ErrorParty.DATAMARKET ||
          item.reqExecNode === info_service.ErrorParty.DMSCRIPT;

        return (
          <Collapse.Panel
            className={styles.panel}
            // bodyBackgroundColor="grey"
            disabled={!hasContent}
            key={item.reqExecNode}
            showArrow={hasContent}
            header={renderHeader(item.reqExecNode, item.status)}
            itemKey={String(item.reqExecNode)}
          >
            {renderPanelContent(item, index)}
          </Collapse.Panel>
        );
      })}
    </Collapse>
  );
};
