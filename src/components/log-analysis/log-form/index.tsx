import { Button, Form, Space, Tooltip, Typography } from '@hi-design/ui';
import { IconExternalOpen, IconHelpCircle } from '@hi-design/ui-icons';
import styles from './index.module.scss';
import { LOG_REGION_OPTIONS } from '@/common/constants/info';
import {
  COMMON_FE_TEXT_MAP,
  INFO_DEBUG_TEXT_MAP,
} from '@/common/constants/I18nTextMap';
import { FC, useRef } from 'react';
import { FormApi } from '@hi-design/ui/es/components/form/types';
import { Status, StatusIcon } from '@/components/status-icon';

export interface LogAnalysisForm {
  logID: string;
  region: string;
  psm: string;
}

export interface ILogAnalysisFormProps {
  isLoading: boolean;
  hasSearchReturned: boolean;
  isSearchSuccess: boolean;
  initialValues?: LogAnalysisForm;
  argosLink?: string;
  handleValueChange: (values: any) => void;
  handleStopSearch: () => void;
  onSubmit: () => void;
}

export const LogAnalysisForm: FC<ILogAnalysisFormProps> = props => {
  const {
    isLoading,
    hasSearchReturned,
    isSearchSuccess,
    initialValues,
    argosLink,
    handleValueChange,
    onSubmit,
    handleStopSearch,
  } = props;
  const { Option } = Form.Select;

  const formAPIRef = useRef<FormApi<LogAnalysisForm>>();

  return (
    <div className={styles.inputBox}>
      <Form
        layout="horizontal"
        initValues={initialValues}
        disabled={isLoading}
        onValueChange={values => handleValueChange(values)}
        onSubmit={onSubmit}
        getFormApi={formApi => (formAPIRef.current = formApi)}
      >
        <Form.Input
          field="logID"
          label={{
            text: 'Log ID',
            extra: (
              <Tooltip content={INFO_DEBUG_TEXT_MAP.INPUT_ANALYSIS_LOG_ID_TIP}>
                <IconHelpCircle style={{ color: 'var(--semi-color-text-2)' }} />
              </Tooltip>
            ),
          }}
          rules={[
            {
              required: true,
              message: COMMON_FE_TEXT_MAP.REQUIRED_FIELD('Log ID'),
            },
          ]}
          style={{ width: 340 }}
        />
        <Form.Select
          field="region"
          label={{
            text: INFO_DEBUG_TEXT_MAP.DEPLOY_REGION,
            extra: (
              <Tooltip content={INFO_DEBUG_TEXT_MAP.INPUT_ANALYSIS_REGION_TIP}>
                <IconHelpCircle style={{ color: 'var(--semi-color-text-2)' }} />
              </Tooltip>
            ),
          }}
          rules={[
            {
              required: true,
              message: COMMON_FE_TEXT_MAP.REQUIRED_FIELD(
                INFO_DEBUG_TEXT_MAP.DEPLOY_REGION,
              ),
            },
          ]}
          style={{ width: 100 }}
        >
          {LOG_REGION_OPTIONS.map(option => (
            <Option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </Option>
          ))}
        </Form.Select>
        <Form.Input
          field="psm"
          label={{
            text: INFO_DEBUG_TEXT_MAP.CALLER_PSM,
            extra: (
              <Tooltip content={INFO_DEBUG_TEXT_MAP.CALLER_PSM_EXTRA}>
                <IconHelpCircle style={{ color: 'var(--semi-color-text-2)' }} />
              </Tooltip>
            ),
          }}
          rules={[
            {
              required: true,
              message: COMMON_FE_TEXT_MAP.REQUIRED_FIELD(
                INFO_DEBUG_TEXT_MAP.CALLER_PSM,
              ),
            },
          ]}
          style={{ width: 200 }}
        />
      </Form>
      <Space className={styles.actions} spacing="medium">
        <Button
          theme="solid"
          loading={isLoading}
          onClick={() => formAPIRef.current?.submitForm()}
        >
          {COMMON_FE_TEXT_MAP.SEARCH}
        </Button>
        {isLoading ? (
          <Button theme="solid" type="danger" onClick={handleStopSearch}>
            {COMMON_FE_TEXT_MAP.STOP_SEARCH}
          </Button>
        ) : null}
        {hasSearchReturned ? (
          <Space spacing={2}>
            {isSearchSuccess ? (
              <>
                <StatusIcon status={Status.SUCCESS} />
                <Typography.Text>
                  {INFO_DEBUG_TEXT_MAP.LOG_FETCH_SUCCESS}
                </Typography.Text>
              </>
            ) : (
              <>
                <StatusIcon status={Status.FAIL} />
                <Typography.Text>
                  {INFO_DEBUG_TEXT_MAP.LOG_FETCH_FAIL}
                </Typography.Text>
              </>
            )}
          </Space>
        ) : null}
        {hasSearchReturned && isSearchSuccess ? (
          <Typography.Text
            link={argosLink ? { href: argosLink, target: '_blank' } : false}
            icon={<IconExternalOpen />}
            underline
            disabled={!argosLink}
          >
            {INFO_DEBUG_TEXT_MAP.ARGOS_LINK}
          </Typography.Text>
        ) : null}
      </Space>
    </div>
  );
};
