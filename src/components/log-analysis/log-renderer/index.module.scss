.<PERSON>g<PERSON><PERSON><PERSON> {
  width: 100%;
  margin-top: 16px;
}

.logTable {
  width: 100%;
  border: 1px solid var(--semi-color-border);
  border-radius: 4px;

  :global {
    .united_info-table-tbody
      > .united_info-table-row.united_info-table-row-expand
      > .united_info-table-row-cell {
      background-color: rgba(var(--semi-grey-0), 0.5);
    }
  }
}

.expandedContent {
  width: 100%;
}

.logEntry {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 8px;
  border: 1px solid var(--semi-color-border);
  border-radius: 4px;
  border-left-width: 4px;
  border-left-color: rgba(var(--semi-grey-1), 1);
  background-color: rgba(var(--semi-grey-0), 1);

  &.error {
    border-left-color: var(--semi-color-danger);
  }
  &.warn {
    border-left-color: var(--semi-color-warning);
  }
  &.info {
    border-left-color: var(--semi-color-primary);
  }

  &:last-child {
    margin-bottom: 0;
  }

  & > span {
    display: block;
    word-break: break-all;
  }
}
