/**
 * 功能：INFO/API通用的查看操作记录抽屉样式,通过列表页通信参数，获取入口来源，数据ID
 * @param visible 控制抽屉展示/关闭
 * @param target 打开抽屉的路径来源，是INFO还是API
 * @param id 查看操作记录的信息ID
 * @param title 抽屉标题
 * @param onCancel 关闭抽屉的回调函数
 */
import { FC } from 'react';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import OptionsRecords from '@components/CustomerRecordsRender';
import { Target } from '@http_idl/infoService';
import { SideSheet } from '@hi-design/ui';

interface ICustomerRecordsModal {
  visible: boolean;
  target: Target;
  id: string;
  title?: string;
  onCancel: () => void;
}

const CustomerRecordsModal: FC<ICustomerRecordsModal> = props => {
  const {
    visible,
    target,
    id,
    title = COMMON_FE_TEXT_MAP.Operation_Record,
    onCancel,
  } = props;
  return (
    <SideSheet
      width="500px"
      title={title}
      visible={visible}
      onCancel={onCancel}
    >
      <OptionsRecords id={id} target={target} />
    </SideSheet>
  );
};

export default CustomerRecordsModal;
