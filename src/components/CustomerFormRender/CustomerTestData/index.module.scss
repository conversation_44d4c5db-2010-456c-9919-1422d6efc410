.CustomerTestData {
  width: 100%;
  &Input {
    margin-bottom: 8px;
  }

  &Operate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &AutoButton {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      :global {
        .united_info-typography {
          margin-right: 8px;
          color: var(--semi-color-link);
        }
      }
    }
    :global {
      .united_info-icons-default {
        color: var(--semi-color-text-3);
        cursor: pointer;

        &:hover {
          color: var(--semi-color-text-2);
        }
      }
    }
  }

  &Result {
    width: 100%;
    margin-top: 8px;
    border: 1px solid var(--semi-color-border);
    border-radius: 6px;
    padding: 12px;

    &Title {
      color: var(--semi-color-text-0);
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 10px;
    }

    &Detail {
      display: block;
      word-break: break-all;
      text-overflow: ellipsis;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 400;
    }
    &Icon {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
}
