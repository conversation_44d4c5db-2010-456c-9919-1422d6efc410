/**
 * 功能：INFO 自动测试数据预览功能组件
 * 支持根据已填写表单数据获取该条INFO的测试内容
 *
 * @param value 在schema中定义的value值，通过ace-form传入到自定义组件中
 * @param onChange value值触发修改时，set到form中
 * @param props scheam中定义的props内容
 */

import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Input, Button, Tooltip, Toast } from '@hi-design/ui';
import { IconHelpCircle, IconTickCircle, IconClear } from '@hi-design/ui-icons';
import { useStores } from '@stores/index';
import { isEmpty, isNil } from 'lodash-es';
import { observer } from 'mobx-react';
import { shared } from '@ies/united-sdk-i18n';

import Styles from './index.module.scss';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { infoServiceClient } from '@http_idl/infoService';

interface ICustomerTestData {
  value: string;
  api: any;
  item?: any;
  isView?: boolean;
  onChange?: (e: any) => void;
}
const enum TestResult {
  SUCCESS,
  FAIL,
}

const CustomerTestData: FC<ICustomerTestData> = props => {
  const { value = {}, isView = false, api, item, onChange } = props || {};
  const { items: options = [], key } = item || {};
  const filedKey = item?.key;
  const disabled = item?.props?.disabled;
  const { formApi } = api?.getExtra() || {};
  const { form } = useStores();
  const formValues = JSON.stringify(form?.formValues);
  const { agent } = shared.getCoreData();
  const [autoLoading, setAutoLoading] = useState<boolean>(false);
  const [autoDisabled, setAutoDisabled] = useState<boolean>(true);
  const [runLoading, setRunLoading] = useState<boolean>(false);
  const [runDisabled, setRunDisabled] = useState<boolean>(true);
  const testResult = JSON.parse(JSON.stringify(form?.apiTestResult));
  const parmasItems = useMemo(() => {
    if (!isNil(form?.apiParams)) {
      if (isEmpty(form?.apiParams)) {
        setAutoDisabled(true);
      } else {
        setAutoDisabled(false);
      }
      onChange?.({});
      try {
        return JSON.parse(form?.apiParams || '{}');
      } catch (e) {
        return {};
      }
    }
    if (!isEmpty(options)) {
      const params = {};
      options.forEach(option => {
        params[option?.field] = option?.label;
      });
      setAutoDisabled(false);
      return params;
    }
    setAutoDisabled(true);
    return {};
  }, [form?.apiParams, JSON.stringify(options)]);

  const handleOnChangeData = useCallback(
    (v: string, item: string) => {
      value[`\${${item}}`] = v;
      onChange?.(value);
    },
    [value, onChange],
  );

  const handleRunData = useCallback(async () => {
    try {
      if (runDisabled || runLoading || disabled) {
        return;
      }
      setRunLoading(true);
      const result = await infoServiceClient.GetInfoTestResult({
        newData: JSON.stringify(formApi?.getValues()),
      });
      if (result?.BaseResp?.StatusCode !== 0) {
        Toast.error(result.BaseResp?.StatusMessage);
        return;
      }
      const { success, infoTestResult = '', failReason = [] } = result || {};
      const testResult = {
        status: success ? TestResult.SUCCESS : TestResult.FAIL,
        detail: success ? [infoTestResult] : failReason,
      };
      form?.setApiTestResult(testResult);
      formApi.scrollToField(key);
    } finally {
      setRunLoading(false);
    }
  }, [formValues, runLoading, runDisabled, disabled, formApi, key]);

  const handleAutoGetData = useCallback(async () => {
    try {
      if (autoLoading || autoDisabled || disabled) {
        return;
      }
      setAutoLoading(true);
      const result = await infoServiceClient.GetInfoTestData({
        newData: JSON.stringify(formApi?.getValues()),
        operateUserMail: `${agent?.Email || agent?.email},${agent?.UserName || agent?.username}`,
      });
      const { infoTestData, infoTestDataSuccess, infoTestDataTip } =
        result || {};
      Object.keys(infoTestData).forEach(item => {
        if (parmasItems[item]) {
          value[`\${${item}}`] = infoTestData[item];
        }
      });
      onChange?.(value);
      if (!infoTestDataSuccess) {
        Toast.error(infoTestDataTip);
      }
    } finally {
      setAutoLoading(false);
    }
  }, [
    value,
    formApi,
    formValues,
    agent,
    parmasItems,
    autoLoading,
    autoDisabled,
    disabled,
  ]);

  useEffect(() => {
    if (
      !api?.getValue('apiId') ||
      !api?.getValue('infoKey') ||
      isEmpty(parmasItems)
    ) {
      setRunDisabled(true);
      return;
    }
    let isTestDataAllInput = true;
    // 测试字段是否都填完了
    Object.keys(parmasItems).map(item => {
      if (!value[`\${${item}}`]) {
        isTestDataAllInput = false;
        return;
      }
    });
    setRunDisabled(!isTestDataAllInput);
  }, [
    parmasItems,
    JSON.stringify(value),
    api?.getValue('apiId'),
    api?.getValue('infoKey'),
  ]);

  return (
    <div className={Styles.CustomerTestData}>
      {// eslint-disable-next-line arrow-body-style
      Object.keys(parmasItems)?.map((item, index) => {
        const prefix = String(
          parmasItems[item]?.name || parmasItems[item] || '-',
        );
        const placeholder = COMMON_FE_TEXT_MAP.Input_Placeholder_Name(prefix);
        let inputValue = value?.[`\${${item}}`] || '';
        if (isView) {
          inputValue = formApi?.getValues()?.[filedKey]?.[`\${${item}}`] || '';
        }
        return (
          <Input
            className={Styles.CustomerTestDataInput}
            key={index}
            disabled={disabled}
            value={inputValue}
            placeholder={placeholder}
            prefix={prefix}
            showClear
            onChange={v => handleOnChangeData(v, item)}
          />
        );
      })}
      <div className={Styles.CustomerTestDataOperate}>
        <Button
          theme="solid"
          type="primary"
          loading={runLoading}
          disabled={runDisabled || disabled}
          onClick={handleRunData}
        >
          {COMMON_FE_TEXT_MAP.Run_Data}
        </Button>
        <div className={Styles.CustomerTestDataOperateAutoButton}>
          <Button
            theme="borderless"
            type="primary"
            loading={autoLoading}
            disabled={autoDisabled || disabled}
            onClick={handleAutoGetData}
          >
            {autoLoading
              ? COMMON_FE_TEXT_MAP.Getting_Data
              : COMMON_FE_TEXT_MAP.Get_Data_Auto}
          </Button>
          <Tooltip content={COMMON_FE_TEXT_MAP.Get_Data_Fail_Tips}>
            <IconHelpCircle />
          </Tooltip>
        </div>
      </div>
      {!isNil(testResult?.status) ? (
        <div className={Styles.CustomerTestDataResult}>
          <div className={Styles.CustomerTestDataResultTitle}>
            {testResult?.status === TestResult.SUCCESS && (
              <div className={Styles.CustomerTestDataResultIcon}>
                <IconTickCircle
                  style={{ color: 'var(--semi-color-success)', marginRight: 9 }}
                />
                {COMMON_FE_TEXT_MAP.Test_Success}
              </div>
            )}
            {testResult?.status === TestResult.FAIL && (
              <div className={Styles.CustomerTestDataResultIcon}>
                <IconClear
                  style={{ color: 'var(--semi-color-danger)', marginRight: 9 }}
                />
                {COMMON_FE_TEXT_MAP.Test_Fail}
              </div>
            )}
          </div>
          <div>
            {testResult?.detail?.map((item, index) => (
              <span className={Styles.CustomerTestDataResultDetail} key={index}>
                {item}
              </span>
            ))}
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default observer(CustomerTestData);
