/**
 * 功能：API接入自定义组件，由于下拉选项样式以及需要去远程获取搜索数据，因此封装成一个自定义组件
 * 支持远程模糊搜索接口获取API数据列表
 * Select Item样式改动，展示label与value
 *
 * @param value 在schema中定义的value值，通过ace-form传入到自定义组件中
 * @param onChange value值触发修改时，set到form中
 * @param props scheam中定义的props内容
 *
 * @function fetchApiOptions 获取API options数据
 *
 * @render renderCustomOptions 渲染自定义下拉选项样式，将label与value全部换行展示出来
 * @render renderSelectedItem 在trigger出样式进行修改，只展示label
 */
import { FC, ReactNode, useCallback, useState } from 'react';
import { Select } from '@hi-design/ui';
import { Api, infoServiceClient, Scene } from '@http_idl/infoService';
import Styles from './index.module.scss';
import { debounce, isFunction, isNil } from 'lodash-es';
import {
  IOptions,
  IProps,
  TOOLS_TYPE_API_LIST,
} from '@common/constants/formSchema';
import { useStores } from '@stores/index';
import { observer } from 'mobx-react';

interface ICustomerApiAccess {
  value: string;
  api: any;
  onChange: (value: string) => void;
  props?: IProps;
  options?: any;
}

const renderCustomOptions = (item: Api | IOptions): ReactNode => {
  // 这里分成两个 id/name value/label 的原因是
  // 后端接口返回的是name id格式，表单协议约定为了统一处理，使用的是label，value
  // 后端可以在schema中设置初始化的options内容展示，需要设置成为IOptions协议格式
  const { name, id, label, value } = item || ({} as any);
  const optionsValue = id || value;
  const optionsLabel = name || label;
  return (
    <Select.Option
      key={optionsValue}
      value={optionsValue}
      showTick={true}
      className={Styles['options-container']}
    >
      <div className={Styles['options-title']}>{optionsLabel}</div>
      <div className={Styles['options-desc']}>{optionsValue}</div>
    </Select.Option>
  );
};

const renderSelectedItem = (optionNode): ReactNode => (
  <span style={{ marginLeft: 8 }}>{optionNode.label}</span>
);

const CustomerApiAccess: FC<ICustomerApiAccess> = props => {
  const { value, props: formProps, options = [], onChange } = props || {};
  const [apiOptions, setApiOptions] = useState<any>(options);
  const [loading, setLoading] = useState(false);
  const { form } = useStores();

  const fetchApiOptions = async (inputValue: string): Promise<void> => {
    setLoading(true);
    try {
      const params = {
        scene: Scene.SOP,
        tenantId: '1',
        potentialName: inputValue || '',
      };
      const request = await infoServiceClient.GetApi(params);
      setApiOptions(request?.apiList || []);
    } catch (error) {
      console.error(error, 'fetchApiOptions error');
    }
    setLoading(false);
  };

  const handleChange = (
    changeValue: string | number | any[] | Record<string, any> | undefined,
  ): void => {
    if (isFunction(onChange)) {
      onChange(changeValue as string);
    }
    const api = apiOptions?.filter(item => item?.id === changeValue)?.[0];
    // 只有当非工具类型的API，才有测试字段
    form?.setApiTestResult({});
    if (!TOOLS_TYPE_API_LIST.includes(api?.type)) {
      form?.setApiParams(api?.initParams || {});
    } else {
      form?.setApiParams({});
    }
  };

  const handleSearch = debounce(fetchApiOptions, 1000);

  const handleClear = useCallback(() => {
    setApiOptions(options || []);
  }, []);

  const handleBlur = (): void => {
    if (isNil(value) && apiOptions?.length <= 0) {
      setApiOptions(options || []);
    }
  };

  return (
    <Select
      {...formProps}
      className={Styles['options-select-content']}
      filter
      remote
      value={value}
      loading={loading}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      onBlur={handleBlur}
      renderSelectedItem={renderSelectedItem}
    >
      {apiOptions.map(item => renderCustomOptions(item))}
    </Select>
  );
};

export default observer(CustomerApiAccess);
