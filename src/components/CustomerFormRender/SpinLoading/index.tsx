/**
 * 功能：表单页面的加载loading页面
 * @param loading 当loading为true时，展示居中旋转效果，否则隐藏
 */

import { FC } from 'react';
import { Spin } from '@hi-design/ui';
import { IconLoading } from '@hi-design/ui-icons';
import Styles from './index.module.scss';

interface ISpinLoading {
  loading: boolean;
  desc?: string;
}
const SpinLoading: FC<ISpinLoading> = props => {
  const { loading, desc } = props;
  if (loading) {
    return (
      <div className={Styles['spin-loading-container']}>
        <div className={Styles['spin-loading-wrapper']}>
          <Spin indicator={<IconLoading />} />
          {desc && <div className={Styles['spin-loading-desc']}>{desc}</div>}
        </div>
      </div>
    );
  }
  return null;
};

export default SpinLoading;
