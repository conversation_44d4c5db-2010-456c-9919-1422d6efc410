.array-items-render-wrapper {
  display: flex;
  justify-content: space-between;
  .array-items-container {
    width: calc(100% - 32px);
    overflow: hidden;
    overflow-x: auto;
    display: flex;
    justify-content: flex-start;
    flex-wrap: no-wrap;
    &::-webkit-scrollbar {
      display: none;
    }
    div {
      display: inline-block !important;
      width: 100%;
      margin-right: 8px;
    }
  }

  .remove-icon {
    position: relative;
    top: 4px;
    background-color: transparent;
    color: var(--semi-color-text-2);
    cursor: pointer;
  }
  :global {
    .united_info-form-field {
      margin-right: 0px !important;
      margin-bottom: 0px !important;
    }
  }
}
.array-content-render-wrapper {
  .add-icon {
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    color: var(--semi-color-primary);
    cursor: pointer;
    :global {
      .united_info-icons {
        margin-right: 10px;
      }
    }
  }
  .add-icon-disabled {
    color: var(--semi-color-disabled-text);
    cursor: not-allowed;
  }
}

.array-render-wrapper {
  :global {
    .united_info-form-vertical,
    .united_info-form-field {
      padding: 4px 0px !important;
    }
  }
}
