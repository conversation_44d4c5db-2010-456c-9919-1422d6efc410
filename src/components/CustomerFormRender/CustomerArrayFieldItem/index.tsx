import { FC, ReactNode } from 'react';
import { But<PERSON> } from '@hi-design/ui';
import { IconMinusCircle, IconPlusCircle } from '@hi-design/ui-icons';
import cn from 'classnames';
import Styles from './index.module.scss';
interface IArrayRender {
  api: any;
  components: ReactNode;
  index?: number;
  text?: string;
  disabled?: boolean;
}

export const ArrayItemsRender: FC<IArrayRender> = props => {
  const { components, api, index, text, disabled } = props;
  const handleClickDeleteItem = (): void => {
    if (disabled) {
      return;
    }
    api.removeItem(index);
  };
  return (
    <div className={Styles['array-items-render-wrapper']}>
      <div className={Styles['array-items-container']}>{components}</div>
      {text && (
        <Button
          disabled={disabled}
          icon={<IconMinusCircle />}
          className={Styles['remove-icon']}
          onClick={handleClickDeleteItem}
        />
      )}
    </div>
  );
};

export const ArrayContentRender: FC<IArrayRender> = props => {
  const { components, api, text, disabled } = props;
  const handleClickAddItem = (): void => {
    if (disabled) {
      return;
    }
    api.addItem();
  };
  return (
    <div className={Styles['array-content-render-wrapper']}>
      {components}
      {text && (
        <span
          className={cn(
            Styles['add-icon'],
            disabled ? Styles['add-icon-disabled'] : '',
          )}
          onClick={handleClickAddItem}
        >
          <IconPlusCircle /> {text}
        </span>
      )}
    </div>
  );
};

export const ArrayRender: FC<IArrayRender> = props => {
  const { components } = props;
  return <div className={Styles['array-render-wrapper']}>{components}</div>;
};
