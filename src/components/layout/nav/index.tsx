import * as React from 'react';
import { useLocation, useHistory } from '@edenx/runtime/router-v5';
import { Nav } from '@hi-design/ui';
import styles from './index.module.scss';

interface NavItem {
  itemKey: string;
  text: string;
  icon: string;
  url: string;
}

const AppNav: React.FC = () => {
  const location = useLocation();
  const history = useHistory();
  const { pathname } = location;

  const navItems: NavItem[] = [
    {
      itemKey: '/info/list',
      text: 'Info管理',
      icon: 'folder',
      url: '/info/list',
    },
    {
      itemKey: '/api/list',
      text: 'Api管理',
      icon: 'setting',
      url: '/api/list',
    },
  ];

  function onSelect(selectedItem: NavItem): void {
    const item = navItems.find(i => i.itemKey === selectedItem.itemKey);
    if (!item) {
      return;
    }
    history.push(item.url);
  }

  return (
    <Nav
      className={styles['app-nav']}
      mode="vertical"
      openKeys={[]}
      selectedKeys={[pathname]}
      items={navItems}
      onSelect={onSelect}
      footer={{
        collapseButton: true,
      }}
    />
  );
};

export default AppNav;
