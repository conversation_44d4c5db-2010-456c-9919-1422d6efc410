/*
 * @Description: 列表某项hover后会出现的组件，组件结构为两个tooltip（分别对应标题和描述），但展示相同内容
 * @Props: value: 列表上某项展示的数据
 * @Props: descOptions: 弹窗内展示的内容结构
 */
import { Typography } from '@hi-design/ui';
import { FC } from 'react';
import styles from './index.module.scss';
interface DescProps {
  value: string;
  desc?: string;
}
const DescTooltip: FC<DescProps> = ({ value, desc }) => {
  const { Text } = Typography;
  // 如果“描述部分”没有定义或者是空字符串，则不展示描述部分
  const content = desc ? (
    <>
      <div className={styles.descTitleText}>{value}</div>
      <div className={styles.descContentText}>{desc}</div>
    </>
  ) : (
    <div className={styles.descTitleText}>{value}</div>
  );
  return (
    <>
      <div>
        <Text
          ellipsis={{
            showTooltip: {
              type: 'popover',
              opts: {
                position: 'topLeft',
                content,
                className: `${styles.descPop}`,
                motion: false,
              },
            },
          }}
          className={styles.descTitleText}
        >
          {value}
        </Text>
      </div>
      {desc && (
        <div>
          <Text
            ellipsis={{
              showTooltip: {
                type: 'popover',
                opts: {
                  position: 'topLeft',
                  content,
                  className: `${styles.descPop}`,
                  motion: false,
                },
              },
            }}
            className={styles.descContentText}
          >
            {desc}
          </Text>
        </div>
      )}
    </>
  );
};
export default DescTooltip;
