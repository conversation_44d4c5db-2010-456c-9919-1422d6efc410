import { Typography } from '@hi-design/ui';
import { FC } from 'react';
import styles from './index.module.scss';
interface TooltipProps {
  value: string;
}
const { Text } = Typography;
const StringTooltip: FC<TooltipProps> = ({ value }) => (
  <Text
    ellipsis={{
      showTooltip: {
        opts: {
          position: 'left',
          className: `${styles.tooltipContent}`,
          motion: false,
        },
      },
    }}
  >
    {value}
  </Text>
);

export default StringTooltip;
