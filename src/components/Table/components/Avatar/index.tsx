import { FC } from 'react';
import { Avatar as HiUIAvatar } from '@hi-design/ui';
import styles from './index.module.scss';
interface AvatarProps {
  value: string;
}
const Avatar: FC<AvatarProps> = ({ value = '' }) => (
  <div>
    {value[0] && (
      <HiUIAvatar size="extra-small" className={styles.avatarImage}>
        {value[0]}
      </HiUIAvatar>
    )}
    <span className={styles.avatarValue}>{value}</span>
  </div>
);

export default Avatar;
