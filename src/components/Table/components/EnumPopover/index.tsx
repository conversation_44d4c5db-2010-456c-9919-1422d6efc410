/*
 * @Description: 列表某项hover后会出现的组件，组件结构为弹窗嵌套枚举列表
 * @Props: value: 列表上某项展示的数据
 * @Props: descOptions: 弹窗内展示的内容结构
 */

import { FC } from 'react';
import { DescOptions } from '@http_idl/infoService';
import { Popover } from '@hi-design/ui';
import styles from './index.module.scss';
interface EnumProps {
  value: string;
  descOptions: DescOptions;
}
const EnumPopover: FC<EnumProps> = ({ value, descOptions }) => {
  /*
    如果枚举列表为空数组或者未定义，直接展示值，不展示popover
    如果descOptions参数未定义，直接展示值，不展示popover
    如果只定义枚举列表的标题，没有定义值，直接展示值，不展示popover
  */
  if (descOptions?.data?.length <= 1) {
    return <div>-</div>;
  }

  // 弹窗内的各个枚举值
  const contentItems = descOptions?.data?.slice(1)?.map((item, index) => (
    <li key={index}>
      <span className={styles.enumItem}>{item[0]}</span>
      <span className={styles.enumItem}>{item[1]}</span>
      <div className={styles.enumItemLine} />
    </li>
  ));
  const content = (
    <div className={styles.enumContainer}>
      {/* 弹窗内的主标题 */}
      {descOptions?.contentTitle && (
        <div className={styles.enumContentTitle}>
          {descOptions.contentTitle}
        </div>
      )}
      {/* 弹窗内的副标题 */}
      {descOptions?.contentDesc && (
        <div className={styles.enumContentDesc}>{descOptions.contentDesc}</div>
      )}
      {/* 弹窗内的枚举值的title */}
      {descOptions?.data[0]?.length && (
        <div>
          <span className={`${styles.enumItem} ${styles.enumTitleItem}`}>
            {descOptions.data[0][0]}
          </span>
          <span className={`${styles.enumItem} ${styles.enumTitleItem}`}>
            {descOptions.data[0][1]}
          </span>
          <div className={styles.enumTitleLine} />
        </div>
      )}
      <ul className={styles.enumList}>{contentItems}</ul>
    </div>
  );
  return (
    <Popover showArrow content={content} position="topLeft">
      <span className={styles.enumValue}>{value}</span>
    </Popover>
  );
};

export default EnumPopover;
