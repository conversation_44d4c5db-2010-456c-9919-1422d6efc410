.enum-container {
  width: 332px;
  overflow: hidden;
  max-height: 300px;
  overflow: auto;
  padding: 4px;
}

.enum-content-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  color: var(--semi-color-text-1);
  margin-bottom: 2px;
}

.enum-content-desc {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: var(--semi-color-text-2);
  margin-bottom: 8px;
}
.enum-title-item {
  color: var(--semi-color-text-2);
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
}
.enum-title-line {
  border: 1px solid var(--semi-color-border);
}
.enum-item {
  display: inline-block;
  height: 37px;
  line-height: 37px;
  width: 150px;
  padding-left: 12px;
}

.enum-item-line {
  border: 1px solid var(--semi-color-border);
  border-bottom: 0;
}
.enum-value {
  color: var(--semi-color-primary);
}

.enum-value:hover {
  color: var(--semi-color-primary-hover);
}

.enum-list {
  display: inline-block;
  padding: 0;
  margin: 0;
}
