/*
 * @Description: 列表上某项hover后会出现的组件，组件结构为弹窗里面嵌套tag数组
 * @Props: value: 在列表上的该项出现的数据
 * @Props: options: tag数组结构
 */

import { Popover, TagGroup } from '@hi-design/ui';
import { FC } from 'react';
import styles from './index.module.scss';
import { OptionItem } from '@http_idl/infoService';
import { Color } from '@hi-design/ui/es/components/tag/types';
interface TaglistProps {
  value: string;
  options: Array<OptionItem>;
}
const TagListPopover: FC<TaglistProps> = ({ value, options }) => {
  const contentItems = options?.map((item, index) => {
    // 当没有定义tag列表或者tag列表为空列表时，这种情况下直接返回空（这里用0来占位）
    if (!item.itemList?.length) {
      return 0;
    }
    // 定义每个tag
    const list = item.itemList.map(ele => ({
      color: 'white' as Color,
      children: ele,
    }));
    return (
      <div key={index} className={styles.tagContent}>
        <div className={styles.tagTitle}>{item.title}</div>
        <TagGroup tagList={list} />
      </div>
    );
  });
  const content = <div className={styles.tagList}>{contentItems}</div>;
  /*
    如果调用情况列表未定义或列表无数据时，直接展示值，没有popover；
    定义的每种调用情况下的tag列表均为空（或未定义），直接展示值，没有popover
  */
  if (!options?.length || !contentItems?.filter(item => item)?.length) {
    return <div>-</div>;
  }
  return (
    <Popover showArrow position="topLeft" content={content}>
      <span className={styles.tagValue}>{value}</span>
    </Popover>
  );
};

export default TagListPopover;
