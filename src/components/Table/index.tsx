/*
 * @Description: 列表组件
 * @Props: data: 包含关于组件的状态（columns: 列表config; listData: 列表list数据; pagination: 列表分页; operateColumns: 列表操作列）
 * @Props: loading: 组件的loading态
 */
import { FC, useMemo } from 'react';
import { Empty, Table } from '@hi-design/ui';
import transferColumns from '@common/utils/transferColumns';
import transferData from '@common/utils/transferData';
import { isNil, isString } from 'lodash-es';
import { Data } from '@http_idl/infoService';
import {
  IllustrationNoResultDark,
  IllustrationNoResult,
} from '@hi-design/ui-illustrations';

import styles from './index.module.scss';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { ManageType } from '@common/utils/starlingKey';
interface ParamProps {
  columns: string;
  listData: Array<Array<Data>>;
  pagination: any;
  operateColumns?: any;
}
interface ListProps {
  data: ParamProps;
  loading: boolean;
  pageLoading: boolean;
  manageType: ManageType;
  tableWidth: number;
}
const ListTable: FC<ListProps> = ({
  data,
  loading,
  pageLoading,
  manageType,
  tableWidth,
}) => {
  const { columns, listData, pagination, operateColumns } = data;
  const col = useMemo(() => {
    if (isString(columns) && columns !== '') {
      const colList = transferColumns(columns, manageType) || [];
      // 如果操作列有数据
      if (!isNil(operateColumns)) {
        colList.push(operateColumns);
      }
      return colList;
    }
    return [];
  }, [columns, manageType]);
  const dataSource = transferData(listData);

  // 用于动态计算列表滑动区域高度。由于列表为固定滑动样式，且筛选项部分高度不确定，因此需要动态计算高度。
  const filterHeight = document.querySelector('.filterContainer')?.offsetHeight;

  // 定义列表的缺省样式
  const empty =
    !loading && !pageLoading ? (
      <Empty
        image={<IllustrationNoResult style={{ width: 150, height: 150 }} />}
        darkModeImage={
          <IllustrationNoResultDark style={{ width: 150, height: 150 }} />
        }
        description={COMMON_FE_TEXT_MAP.List_Empty}
      />
    ) : (
      <div />
    );

  return (
    <>
      {columns && (
        <Table
          style={{ width: tableWidth }}
          columns={col}
          dataSource={dataSource}
          pagination={pagination}
          loading={loading}
          empty={empty}
          className={styles.table}
          scroll={{
            y: `calc(100vh - ${filterHeight}px - 240px)`,
            scrollToFirstRowOnChange: true,
          }}
        />
      )}
    </>
  );
};

export default ListTable;
