import { FC, memo } from 'react';
import { Empty } from '@hi-design/ui';
import {
  IllustrationNoResultDark,
  IllustrationNoResult,
} from '@hi-design/ui-illustrations';

import Styles from './index.module.scss';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
const SchemaError: FC = () => (
  <Empty
    image={<IllustrationNoResult style={{ width: 150, height: 150 }} />}
    darkModeImage={
      <IllustrationNoResultDark style={{ width: 150, height: 150 }} />
    }
    description={
      <>
        <h3 className={Styles.title}>{COMMON_FE_TEXT_MAP.No_Form_Data}</h3>
        <span className={Styles.desc}>
          {COMMON_FE_TEXT_MAP.No_Form_Data_Tips}
        </span>
      </>
    }
  />
);
export default memo(SchemaError);
