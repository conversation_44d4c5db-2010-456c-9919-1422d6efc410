import * as React from 'react';
import { Route, RouteProps } from '@edenx/runtime/router-v5';
import RouteGuard, { RouteGuardProps } from './RouteGuard';

export interface AuthReturnType {
  message?: string;
  permission: boolean;
}

export interface RouteConfig extends Omit<RouteProps, 'children'> {
  path: string;
  children?: RouteConfig[];
  authMethod?: (props: RouteGuardProps) => Promise<AuthReturnType>;
  noPermission?: (props: RouteConfig) => React.ReactNode | React.ReactNode;
}

interface IProps {
  routeConfig: RouteConfig[];
}

class RouteRender extends React.Component<IProps> {
  genRoutes = (routeConfig: RouteConfig[] = [], rootPath = ''): any[] =>
    routeConfig.map(item => {
      let routers = [];
      const { sensitive, strict } = item;
      if (item.component) {
        routers.push(
          <Route
            key={item.path}
            sensitive={sensitive}
            strict={strict}
            exact
            path={`${rootPath}${item.path}`}
            render={(): JSX.Element => <RouteGuard {...item} />}
          />,
        );
      }
      if (item.children && item.children.length > 0) {
        routers = routers.concat(
          this.genRoutes(item.children, rootPath + item.path),
        );
      }
      return routers;
    });

  render() {
    const { routeConfig } = this.props;
    return <React.Fragment>{this.genRoutes(routeConfig)}</React.Fragment>;
  }
}

export default RouteRender;
