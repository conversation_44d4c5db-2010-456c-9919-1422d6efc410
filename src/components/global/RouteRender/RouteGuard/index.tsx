import * as React from 'react';
import { RouteComponentProps, withRouter } from '@edenx/runtime/router-v5';
import { Spin } from '@hi-design/ui';
import { RouteConfig } from '../index';
import styles from './index.module.scss';

interface IState {
  permission: boolean;
  loading: boolean;
  message: string;
}

export type RouteGuardProps = RouteConfig & RouteComponentProps;

class RouteGuard extends React.Component<RouteGuardProps, IState> {
  unMount = false;
  constructor(props) {
    super(props);
    const { authMethod } = this.props;
    this.state = {
      permission: true,
      message: '',
      loading: Boolean(authMethod),
    };
  }
  componentDidMount(): void {
    this.auth();
  }

  componentWillUnmount(): void {
    this.unMount = true;
  }

  auth = (): void => {
    const { authMethod } = this.props;
    if (authMethod) {
      this.setState({ loading: true });
      authMethod({ ...this.props })
        .then(result => {
          if (!this.unMount) {
            const { permission, message } = result;
            if (permission) {
              this.setState({ permission: true });
            } else {
              this.setState({
                permission: false,
                message,
              });
            }
          }
        })
        .finally(() => !this.unMount && this.setState({ loading: false }));
    }
  };

  renderNoPermission = (): any => {
    const { noPermission } = this.props;
    const { message } = this.state;
    if (!noPermission) {
      return <div style={{ textAlign: 'center' }}>{message}</div>;
    }
    if (typeof noPermission === 'function') {
      return noPermission(this.props);
    }
    return noPermission;
  };

  renderChildren = (): React.ReactElement => {
    const { component } = this.props;
    const { permission, loading } = this.state;
    if (loading) {
      return null;
    }
    if (!permission) {
      return this.renderNoPermission();
    }
    return React.createElement(component, this.props);
  };

  render(): React.ReactElement {
    const { loading } = this.state;
    return (
      <Spin spinning={loading} size="large" wrapperClassName={styles.spin}>
        {this.renderChildren()}
      </Spin>
    );
  }
}

export default withRouter(RouteGuard);
