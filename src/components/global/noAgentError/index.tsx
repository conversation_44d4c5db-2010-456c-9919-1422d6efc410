import { FC, memo } from 'react';
import { Empty } from '@hi-design/ui';
import {
  IllustrationNoAccess,
  IllustrationNoAccessDark,
} from '@hi-design/ui-illustrations';

import Styles from './index.module.scss';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
const NoAgentError: FC = () => (
  <Empty
    image={<IllustrationNoAccess style={{ width: 150, height: 150 }} />}
    darkModeImage={
      <IllustrationNoAccessDark style={{ width: 150, height: 150 }} />
    }
    description={
      <>
        <h3 className={Styles.title}>{COMMON_FE_TEXT_MAP.No_Perms}</h3>
        <span className={Styles.desc}>{COMMON_FE_TEXT_MAP.No_Perms_Tips}</span>
      </>
    }
  />
);
export default memo(NoAgentError);
