import * as React from 'react';
import { Spin } from '@hi-design/ui';
import styles from './index.module.scss';
import { IconLoading } from '@hi-design/ui-icons';

const PageLoading: React.FC = () => {
  const antIcon = <IconLoading style={{ fontSize: 24 }} spin />;
  return (
    <div className={styles.pageLoading}>
      <Spin indicator={antIcon} />
    </div>
  );
};

interface Props {
  location: string;
}

// 组件延迟加载
// eslint-disable-next-line @typescript-eslint/naming-convention
const LoadableComp = (LazyComponent): React.ReactElement =>
  class LoadComponent extends React.Component<Props> {
    constructor(props, context) {
      super(props, context);
      this.state = {};
    }

    shouldComponentUpdate(nextProps): boolean {
      const { location } = this.props;
      return nextProps.location !== location;
    }

    render(): React.ReactElement {
      return (
        <React.Suspense fallback={<PageLoading />}>
          <LazyComponent {...this.props} />
        </React.Suspense>
      );
    }
  };

export default LoadableComp;
