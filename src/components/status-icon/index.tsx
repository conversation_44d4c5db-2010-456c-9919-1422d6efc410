import { IconTickCircle, IconUploadError } from '@hi-design/ui-icons';
import styles from './index.module.scss';
import { FC } from 'react';

export enum Status {
  SUCCESS = 'success',
  FAIL = 'fail',
}

export interface StatusIconProps {
  status: Status;
}

export const StatusIcon: FC<StatusIconProps> = ({ status }) => {
  if (status === Status.SUCCESS) {
    return (
      <IconTickCircle className={`${styles.statusIcon} ${styles.success}`} />
    );
  }
  if (status === Status.FAIL) {
    return (
      <IconUploadError className={`${styles.statusIcon} ${styles.error}`} />
    );
  }
  return null;
};
