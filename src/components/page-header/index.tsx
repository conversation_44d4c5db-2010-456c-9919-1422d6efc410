// Kefu-Page-Header HIUI版
// 来源： https://bnpm.bytedance.net/package/@ies/kefu-page-header-semi2?tab=readme
import React from 'react';
import { useHistory } from '@edenx/runtime/router-v5';
import { IconChevronLeft } from '@hi-design/ui-icons';
import styles from './index.module.scss';

interface Props {
  title?: string | React.ReactNode;
  backText?: string | React.ReactNode;
  description?: string | React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  onBackClick?: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
}

const defaultTitle = '标题缺失';

const PageHeader: React.FC<Props> = (props: Props) => {
  const history = useHistory();
  const defaultOnBackClick = (): void => {
    history.goBack();
  };

  const {
    title = defaultTitle,
    backText,
    description,
    style,
    className = '',
    onBackClick = defaultOnBackClick,
  } = props;
  return (
    <div className={`${styles['kefu-page-header']} ${className}`} style={style}>
      {backText ? (
        <div className={styles['kefu-page-header-back']} onClick={onBackClick}>
          <IconChevronLeft className={styles['kefu-page-header-back-icon']} />
          <span>{backText}</span>
        </div>
      ) : null}
      <div className={styles['kefu-page-header-title']}>{title}</div>
      {description ? (
        <div className={styles['kefu-page-header-description']}>
          {description}
        </div>
      ) : null}
    </div>
  );
};

export default PageHeader;
export { PageHeader };
