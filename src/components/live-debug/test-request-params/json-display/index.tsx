import { FC } from 'react';
import styles from './index.module.scss';
import { Button, Toast, Tooltip } from '@hi-design/ui';
import { IconCopy } from '@hi-design/ui-icons';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';

export const JsonDisplay: FC<{
  jsonData: string;
  maxHeight?: number;
  half?: boolean;
  copiable?: {
    tooltip?: string;
    successText?: string;
    failText?: string;
  };
}> = ({ jsonData, copiable, half, maxHeight }) => {
  // Copy JSON to clipboard
  const copyJsonToClipboard = async () => {
    try {
      await navigator?.clipboard?.writeText?.(jsonData);
      Toast.success(
        copiable?.successText || INFO_DEBUG_TEXT_MAP.COPIED_TO_CLIPBOARD,
      );
    } catch (error) {
      Toast.error(copiable?.failText || INFO_DEBUG_TEXT_MAP.COPY_FAILED);
    }
  };

  return (
    <div
      className={styles.jsonContainer}
      style={half ? { width: '50%' } : { width: '100%' }}
    >
      {copiable ? (
        <Tooltip content={copiable?.tooltip || 'Copy JSON'}>
          <Button
            className={styles.copyButton}
            icon={<IconCopy />}
            type="tertiary"
            size="small"
            onClick={copyJsonToClipboard}
          />
        </Tooltip>
      ) : null}

      <pre
        className={styles.jsonContent}
        style={maxHeight ? { maxHeight } : {}}
      >
        {jsonData}
      </pre>
    </div>
  );
};
