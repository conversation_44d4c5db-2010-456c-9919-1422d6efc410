import { FC } from 'react';
import styles from './index.module.scss';
import TestRequestForm from './test-request-form';
import { JsonDisplay } from './json-display';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';

export interface TestRequestParamsProps {
  requestDataConfig: Record<string, info_service.InfoField>;
  requestDataStr: string;
  isResultLoading: boolean;
  initRequestDataFormValues: Record<string, string>;
  onRequestDataFormChange: (values: Record<string, string>) => void;
  handleExecuteRequest: (values: Record<string, string>) => void;
}

export const TestRequestParams: FC<TestRequestParamsProps> = ({
  requestDataConfig,
  requestDataStr,
  isResultLoading,
  initRequestDataFormValues,
  onRequestDataFormChange,
  handleExecuteRequest,
}) => (
  <div className={styles.testRequestContainer}>
    <TestRequestForm
      requestDataConfig={requestDataConfig}
      isResultLoading={isResultLoading}
      initRequestDataFormValues={initRequestDataFormValues}
      onRequestDataFormChange={onRequestDataFormChange}
      handleExecuteRequest={handleExecuteRequest}
    />

    <JsonDisplay
      maxHeight={400}
      half
      copiable={{
        tooltip: INFO_DEBUG_TEXT_MAP.COPY_PARAMS_JSON_TOOLTIP,
        successText: INFO_DEBUG_TEXT_MAP.PARAMS_JSON_COPIED,
        failText: INFO_DEBUG_TEXT_MAP.PARAMS_JSON_COPY_FAILED,
      }}
      jsonData={requestDataStr}
    />
  </div>
);

export default TestRequestParams;
