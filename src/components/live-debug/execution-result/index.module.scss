.spinWrapper {
  width: 50% !important;

  .spinContainer {
    min-height: 100px;
    display: flex;
    flex-direction: column;

    border: 1px solid var(--semi-color-border);
    border-radius: 4px;
    padding: 16px;

    & > .emptyText {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      flex: 1;
    }

    .title {
      margin-bottom: 8px;
    }
  }
}
