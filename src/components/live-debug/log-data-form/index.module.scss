.container {
  width: 825px;
  .collapseHeader {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--semi-color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    &:hover {
      cursor: pointer;
    }

    & > .headerText {
      & > .title {
        display: inline-flex;
        align-items: center;

        & > .titleIcon {
          margin-left: 8px;
        }
      }
    }
  }

  .formContainer {
    padding: 0 8px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;

    .formActions {
      margin-top: 24px;
    }
  }

  .helpIcon {
    color: var(--semi-color-text-2);
  }
}
