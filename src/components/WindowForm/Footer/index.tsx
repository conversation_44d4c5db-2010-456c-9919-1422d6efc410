import { FC, memo, useCallback, useMemo } from 'react';
import { Button } from '@hi-design/ui';
import { Target } from '@http_idl/infoService';
import {
  COMMON_FE_TEXT_MAP,
  TranslateButtonType,
} from '@common/constants/I18nTextMap';
import cn from 'classnames';
import Style from './index.module.scss';
import { StatusType } from '@common/constants/listConfig';
import { TranslateButton } from '@ies/csp-translate-gen-semi2';
import { getFieldType } from '@common/utils/starlingKey';
import { isNil } from 'lodash-es';
interface IWindowFormFooter {
  target: Target; // 最新版本，按钮不需要区分Target，但是保留参数入口
  submitLoading: boolean;
  saveLoading: boolean;
  status: StatusType;
  className?: string;
  isEdit?: boolean;
  filedMap?: any;
  onSubmit: () => void;
  onSave: () => void;
  onCancel: () => void;
  onDivert?: () => void; // 流转
  onClickLang: (data: any, type: TranslateButtonType) => void;
  onBeforeTranslation: () => void;
}

const FormFooter: FC<IWindowFormFooter> = props => {
  const {
    submitLoading,
    saveLoading,
    className,
    isEdit,
    status,
    target,
    filedMap,
    onBeforeTranslation = Noop,
    onSubmit = Noop,
    onSave = Noop,
    onCancel = Noop,
    onDivert = Noop,
    onClickLang = Noop,
  } = props || {};

  const { isHaveAgent, isHaveUser } = getFieldType(filedMap);

  const formatSubmitText = useCallback(() => {
    if (Number(status) === StatusType.INCONFIG) {
      return COMMON_FE_TEXT_MAP.Release;
    }
    return `${isEdit ? COMMON_FE_TEXT_MAP.Update : COMMON_FE_TEXT_MAP.Release}`;
  }, [isEdit, status]);

  const renderTransButton = useMemo(() => {
    if (isHaveUser && isHaveAgent) {
      return (
        <TranslateButton
          className={Style.FormTransButton}
          beforeClick={onBeforeTranslation}
          agent={{
            jsonSchema: '',
            onChange: (langData: any) =>
              onClickLang(langData, TranslateButtonType.AGENT),
          }}
          user={{
            jsonSchema: '',
            onChange: (langData: any) =>
              onClickLang(langData, TranslateButtonType.USER),
          }}
        />
      );
    }
    if (isHaveUser && !isHaveAgent) {
      return (
        <TranslateButton
          className={Style.FormTransButton}
          beforeClick={onBeforeTranslation}
          user={{
            jsonSchema: '',
            onChange: (langData: any) =>
              onClickLang(langData, TranslateButtonType.USER),
          }}
        />
      );
    }
    if (isHaveAgent && !isHaveUser) {
      return (
        <TranslateButton
          className={Style.FormTransButton}
          beforeClick={onBeforeTranslation}
          agent={{
            jsonSchema: '',
            onChange: (langData: any) =>
              onClickLang(langData, TranslateButtonType.AGENT),
          }}
        />
      );
    }
    return null;
  }, [target, isHaveAgent, isHaveUser, onClickLang, onBeforeTranslation]);

  const renderSaveButton = useMemo(() => {
    // INFO的新建&配置中的INFO有新增的功能按钮
    if (Number(status) === StatusType.INCONFIG) {
      return (
        <>
          <Button
            type="tertiary"
            style={{ marginRight: '12px' }}
            loading={saveLoading}
            onClick={onSave}
          >
            {COMMON_FE_TEXT_MAP.Save}
          </Button>
          {target === Target.Info && (
            <Button
              type="tertiary"
              style={{ marginRight: '12px' }}
              onClick={onDivert}
            >
              {COMMON_FE_TEXT_MAP.Divert}
            </Button>
          )}
        </>
      );
    }
    return null;
  }, [isEdit, target, saveLoading, status, onSave, onDivert, onCancel]);

  return (
    <div className={cn(Style.FormFooter, className)}>
      <div>{renderTransButton}</div>
      <div>
        <Button
          type="tertiary"
          style={{ marginRight: '12px' }}
          onClick={onCancel}
        >
          {COMMON_FE_TEXT_MAP.Cancel}
        </Button>
        {renderSaveButton}
        {!isNil(status) && (
          <Button theme="solid" onClick={onSubmit} loading={submitLoading}>
            {formatSubmitText()}
          </Button>
        )}
      </div>
    </div>
  );
};

export default memo(FormFooter);
