import { FC, memo } from 'react';
import cn from 'classnames';
import Style from './index.module.scss';
import SpinLoading from '@components/CustomerFormRender/SpinLoading';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { AceSemiV2Form } from '@byted-ace/form/dist/semiV2';

interface IWindowFormRender {
  formSchema: any;
  viewSchema: any;
  isView: boolean;
  formApiRef: any;
  viewFormApiRef: any;
  initValue: any;
  style?: React.CSSProperties;
  submitLoading: boolean;
  saveLoading: boolean;
  className?: string;
  onValueChange: (_valus, field) => void;
}

const FormRender: FC<IWindowFormRender> = props => {
  const {
    className,
    formSchema,
    viewSchema,
    isView,
    style = {},
    initValue,
    formApiRef,
    viewFormApiRef,
    saveLoading,
    submitLoading,
    onValueChange,
  } = props || {};
  return (
    <div
      className={cn(Style.formContainer, 'customer-form-container', className)}
      style={style}
    >
      <SpinLoading
        loading={saveLoading || submitLoading}
        desc={submitLoading && COMMON_FE_TEXT_MAP.Form_Save_Loading}
      />
      {isView && (
        <AceSemiV2Form
          forms={viewSchema}
          getFormApi={(formApi): any => (viewFormApiRef.current = formApi)}
          initValues={initValue}
          formProps={{
            layout: 'vertical',
            labelWidth: 400,
            labelAlign: 'left',
            labelPosition: 'top',
            allowEmpty: true,
            className: 'ace-form-wrapper',
          }}
        />
      )}
      {!isView && (
        <AceSemiV2Form
          forms={formSchema}
          getFormApi={(formApi): any => (formApiRef.current = formApi)}
          initValues={initValue}
          onValueChange={onValueChange}
          formProps={{
            layout: 'vertical',
            labelWidth: 400,
            labelAlign: 'left',
            labelPosition: 'top',
            allowEmpty: true,
            className: 'ace-form-wrapper',
          }}
        />
      )}
    </div>
  );
};

export default memo(FormRender);
