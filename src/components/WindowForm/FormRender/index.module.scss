.formContainer {
  height: 100vh;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 0 !important;
  }
  :global {
    .no_bottom_space {
      margin-bottom: 0px !important;
      padding-bottom: 0px !important;
    }
    .radio-card-2,
    .radio-card-more {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      :global {
        .united_info-radio-inner {
          position: relative;
          text-overflow: ellipsis;
        }
        .united_info-radio-addon {
          margin-left: 0;
        }
        .united_info-radio-checked {
          background-color: #fff;
          border: 1px solid var(--semi-color-focus-border) !important;
        }

        .united_info-radio {
          border-radius: 5px;
          border: 1px solid var(--semi-color-fill-0);
          margin-right: 0px;
          padding: 7px 12px;
        }
      }
    }
    .radio-card-1 {
      :global {
        .united_info-radio {
          width: 100% !important;
        }
      }
    }
    .radio-card-2 {
      :global {
        .united_info-radio {
          min-width: 46% !important;
          margin: 4px;
        }
      }
    }
    .radio-card-more {
      padding-top: 0px;
      padding-bottom: 0px;
      :global {
        .united_info-radio {
          min-width: 30% !important;
          margin: 4px;
        }
      }
    }
    .united_info-form-field-error-message {
      word-break: break-all;
    }
  }
}
