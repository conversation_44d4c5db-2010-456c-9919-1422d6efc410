import { STATUS_MAP, StatusType } from '@common/constants/listConfig';
import { Tag } from '@hi-design/ui';
import { FC, useMemo, memo } from 'react';
import Style from './index.module.scss';

interface IFormTitle {
  title: string;
  status?: StatusType;
  className?: string;
}

const FormTitle: FC<IFormTitle> = props => {
  const { title, status, className = '' } = props || {};
  const StatusInfo = useMemo(() => STATUS_MAP[status], [status]);
  return (
    <div className={`${Style.FormTitle} ${className}`}>
      <span className={Style.FormTitleName}>{title}</span>
      {StatusInfo && (
        <Tag
          style={{
            display: 'table',
          }}
          color={StatusInfo?.color}
        >
          {StatusInfo?.name}
        </Tag>
      )}
    </div>
  );
};

export default memo(FormTitle);
