import { FC, memo } from 'react';
import Style from './index.module.scss';
import { IconChevronLeft } from '@hi-design/ui-icons';

interface IBackNav {
  title: string;
  style?: React.CSSProperties;
  onBack: () => void;
}

const BackNav: FC<IBackNav> = props => {
  const { title, style, onBack } = props;
  return (
    <div className={Style.BackNav} style={style} onClick={onBack}>
      <IconChevronLeft />
      {title}
    </div>
  );
};

export default memo(BackNav);
