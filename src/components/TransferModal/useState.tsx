import { formatNilValueToString } from '@common/utils/format';
import {
  infoServiceClient,
  PermissionPoint,
  TransferUser,
} from '@http_idl/infoService';
import { debounce, isNil } from 'lodash-es';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { shared } from '@ies/united-sdk-i18n';
import { Toast } from '@hi-design/ui';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';

export interface UseTransferStateProps {
  visible: boolean;
  formApiRef: any;
  viewFormApiRef: any;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  SubmitValueRef: any;
  starlingMapRef: any;
  infoId: string;
  isView: boolean;
  permission: PermissionPoint;
  onFormCancel?: () => void;
  setInfoId?: (infoId: string) => void;
  onDivertSuccess?: (infoId: string) => void;
  fetchFormListData?: () => Promise<void>;
  onCancel: () => void;
}

export const useTransferState = (props: UseTransferStateProps) => {
  const {
    isView,
    formApiRef,
    viewFormApiRef,
    starlingMapRef,
    SubmitValueRef,
    infoId,
    permission,
    onCancel: onTransferModalCancel,
    onFormCancel,
    fetchFormListData,
    setInfoId,
    onDivertSuccess,
  } = props;
  const { agent } = shared.getCoreData();

  const [transferUserInfo, setTransferUserInfo] = useState<TransferUser>(
    {} satisfies TransferUser,
  );
  const [options, setOptions] = useState<TransferUser[]>([]);
  const [selectValue, setSelectValue] = useState<TransferUser>({});

  const [isShowError, setIsShowError] = useState<boolean>(false);

  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const handleOnConfirm = () => {
    if (isNil(transferUserInfo?.email) || transferUserInfo?.email === '') {
      setIsShowError(true);
      return;
    }
    try {
      const api = isView ? viewFormApiRef : formApiRef;
      // 先去调用保存接口，后去执行流转功能
      api?.current
        ?.validate()
        .then(async (values: any) => {
          setConfirmLoading(true);
          let value = {} as any;
          if (isView) {
            value = SubmitValueRef?.current;
          } else {
            value = values;
          }
          formatNilValueToString(values);
          value.starlingMap = starlingMapRef?.current || {};

          const toastTitle = values?.name || '-';
          const params = {
            data: JSON.stringify(values),
            permissionPoint: permission,
            operateUserMail: `${agent?.Email || agent?.email},${agent?.UserName || agent?.username}`,
          } as any;
          if (infoId) {
            params.infoId = infoId;
          }
          const { BaseResp, infoId: SaveInfoId } =
            await infoServiceClient.SaveInfo(params);
          if (BaseResp?.StatusCode === 0) {
            setInfoId?.(SaveInfoId);
            // 执行流转接口
            const { BaseResp: TranserResp } =
              await infoServiceClient.TransferInfo({
                infoId: SaveInfoId,
                operateTransferUserMail: `${agent?.Email || agent?.email},${agent?.UserName || agent?.username}`,
                transferToUserMail: `${transferUserInfo?.email},${transferUserInfo?.name}`,
              });
            if (TranserResp?.StatusCode === 0) {
              Toast.success(
                `「${toastTitle || '-'}」${COMMON_FE_TEXT_MAP.Form_Trans_Success}`,
              );
              onFormCancel?.();
              onDivertSuccess?.(SaveInfoId);
            } else {
              Toast.error(TranserResp?.StatusMessage);
              fetchFormListData?.();
              onTransferModalCancel?.();
            }
          } else {
            Toast.error(BaseResp?.StatusMessage);
            onTransferModalCancel?.();
          }
          setConfirmLoading(false);
        })
        .catch((errorValues: any) => {
          Toast.error(COMMON_FE_TEXT_MAP.Form_Check_Error);
          onTransferModalCancel();
          setConfirmLoading(false);
          formApiRef?.current?.scrollToField(Object.keys(errorValues)[0]);
        });
    } catch (error) {
      console.error('error:', error);
    }
  };

  const handleOnChange = useCallback(
    (data: TransferUser) => {
      setIsShowError(false);
      setSelectValue(data);
      setTransferUserInfo({
        email: data?.email,
        name: data?.name,
      });
    },
    [options],
  );

  const handleOnSearch = useCallback(
    debounce(async inputValue => {
      try {
        setSearchLoading(true);
        const result = await infoServiceClient.SearchTransferUser({
          searchContent: inputValue,
        });
        const { transferUserList = [] } = result || {};
        setOptions(transferUserList);
      } finally {
        setSearchLoading(false);
      }
    }, 800),
    [],
  );

  useEffect(() => {
    handleOnSearch('');
  }, []);

  return useMemo(
    () => ({
      isShowError,
      options,
      searchLoading,
      confirmLoading,
      selectValue,
      handleOnConfirm,
      handleOnChange,
      handleOnSearch,
    }),
    [
      isShowError,
      options,
      searchLoading,
      confirmLoading,
      selectValue,
      handleOnConfirm,
      handleOnSearch,
      handleOnChange,
    ],
  );
};
