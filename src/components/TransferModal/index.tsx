/**
 * @description 流转面板，在INFO管理中，新建 与 编辑配置中的INFO，具有流转按钮，点击唤起流转面板
 *
 */
import { FC, memo } from 'react';
import { COMMON_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { Select, Modal, Spin, Avatar } from '@hi-design/ui';
import Style from './index.module.scss';
import { useTransferState, UseTransferStateProps } from './useState';
import { IconAlertCircle } from '@hi-design/ui-icons';

const renderSelectedItem = optionNode => (
  <div className={Style.renderSelectedItem}>
    <span className={Style.renderSelectedItemName}>
      {optionNode?.name || '-'}
    </span>
  </div>
);

const renderCustomOption = item => (
  <Select.Option
    className={Style.renderOptions}
    key={item?.email}
    value={item?.email}
    showTick={true}
    {...item}
  >
    <Avatar size="extra-small">{item?.name?.slice(0, 1)}</Avatar>
    <div style={{ marginLeft: 8 }}>
      <div className={Style.renderOptionsName}>{item?.name || '-'}</div>
      <div className={Style.renderOptionsEmail}>{item.email}</div>
    </div>
  </Select.Option>
);

const renderError = () => (
  <div className={Style.errorTips}>
    <IconAlertCircle className={Style.errorTipsIcon} />
    <span>{COMMON_FE_TEXT_MAP.Trans_Rules_Msg}</span>
  </div>
);

const TransferModal: FC<UseTransferStateProps> = props => {
  const { visible, onCancel } = props;
  const {
    isShowError,
    searchLoading,
    confirmLoading,
    options,
    selectValue,
    handleOnConfirm,
    handleOnChange,
    handleOnSearch,
  } = useTransferState({
    ...props,
  });

  return (
    <>
      <Modal
        size="small"
        title={COMMON_FE_TEXT_MAP.Divert}
        visible={visible}
        confirmLoading={confirmLoading}
        onCancel={onCancel}
        okText={COMMON_FE_TEXT_MAP.Confirm}
        onOk={handleOnConfirm}
      >
        <Spin spinning={false}>
          <Select
            placeholder={COMMON_FE_TEXT_MAP.Trans_Placeholder}
            style={{ width: '100%' }}
            filter
            remote
            onChangeWithObject
            value={selectValue}
            showClear
            loading={searchLoading}
            onSearch={handleOnSearch}
            onChange={handleOnChange}
            emptyContent={<div>{COMMON_FE_TEXT_MAP.Empty_Data}</div>}
            renderSelectedItem={renderSelectedItem}
          >
            {options.map((item, index) => renderCustomOption(item, index))}
          </Select>
          {isShowError && renderError()}
          <div className={Style.transerTips}>
            {COMMON_FE_TEXT_MAP.Trans_Tips}
          </div>
        </Spin>
      </Modal>
    </>
  );
};

export default memo(TransferModal);
