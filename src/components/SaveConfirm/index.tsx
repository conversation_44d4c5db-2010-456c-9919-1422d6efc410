/**
 * 保存提示 modal
 */
import { Modal } from '@hi-design/ui';
import I18n from '@ies/starling_intl';

let isConfirmPending = false;

export function confirm(message: string, callback: (result: boolean) => void) {
  if (isConfirmPending) {
    callback(false);
  } else {
    const [title, content = title] = message.split('|');
    isConfirmPending = true;

    Modal.warning({
      title,
      content,
      okText: I18n.t('common_confirm', {}, '确认'),
      cancelText: I18n.t('common_cancel', {}, '取消'),
      onOk: () => {
        isConfirmPending = false;
        callback(true);
      },
      onCancel: () => {
        isConfirmPending = false;
        callback(false);
      },
    });
  }
}

export default confirm;
