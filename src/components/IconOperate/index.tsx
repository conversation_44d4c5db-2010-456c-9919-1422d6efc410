import React from 'react';
import cls from 'classnames';
import Styles from './index.module.scss';
import { IconClear as Clear, IconChevronDown } from '@hi-design/ui-icons';

export const IconClear: React.FC<{ clickCancel: any }> = ({ clickCancel }) => (
  <Clear
    className={cls({ [Styles.IconWrapper]: true })}
    onClick={clickCancel as any}
  />
);

export const IconDown: React.FC<{ isExpanded: boolean }> = ({ isExpanded }) => (
  <IconChevronDown
    className={cls({
      [Styles.indicateIcon]: true,
      [Styles.IconWrapper]: true,
      [Styles.Expanded]: isExpanded,
    })}
  />
);
