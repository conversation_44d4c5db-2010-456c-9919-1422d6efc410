import React from 'react';
import { Html, Root, Head, Body, Scripts } from '@edenx/runtime/document';

export default function Document(): React.ReactElement {
  return (
    <Html lang="en">
      <Head>
        <title>CSP Info Management</title>
        <meta name="theme-color" content="#000000" />
        <meta name="theme-color2" content="#000000" />
      </Head>
      <Body>
        <script nonce="{{ $nonce }}">window.firstPaint = Date.now()</script>
        <div className="united_info_management-light-scrollbar">
          <Root rootId="root" />
        </div>
        <Scripts />
      </Body>
    </Html>
  );
}
