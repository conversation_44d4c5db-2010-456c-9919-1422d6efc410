import { makeAutoObservable } from 'mobx';

export interface IFormStores {
  apiParams: {
    [key: string]: {
      name?: string;
    };
  } | null;
  apiTestResult: any;
  formValues: {
    [key: string]: any;
  };
  setFormValues: (formValues: any) => void;
  setApiParams: (apiParams: any) => void;
  setApiTestResult: (result: any) => void;
}

export class FormStores implements IFormStores {
  apiParams: IFormStores['apiParams'] = null;
  apiTestResult: any = {};
  formValues: { [key: string]: any } = {};
  rootStore: any;

  constructor(rootStore) {
    makeAutoObservable(this, {}, { autoBind: true });
    this.rootStore = rootStore;
  }

  setApiParams(apiParams: any): void {
    this.apiParams = apiParams;
  }

  setApiTestResult(result: any): void {
    this.apiTestResult = result;
  }

  setFormValues(formValues: any): void {
    this.formValues = formValues;
  }
}
