import { makeAutoObservable } from 'mobx';
import { shared } from '@ies/united-sdk-i18n';
import { isNil } from 'lodash-es';
import {
  AccessParty,
  Agent,
} from '@ies/united-sdk-i18n/dist/types/shared/store/reducers/core';

export interface IUnifiedStores {
  agent: Agent | null;
  accessPartyId: string;
  accessPartyIdList: AccessParty[];
  getAccessPartyId: () => void;
  setGarUser: () => void;
}

export class UnifiedStores implements IUnifiedStores {
  agent: Agent | null = null;
  accessPartyId = '15';
  accessPartyIdList: any[] = [];
  rootStore: any;

  constructor(rootStore) {
    makeAutoObservable(this, {}, { autoBind: true });
    this.rootStore = rootStore;
  }

  getAccessPartyId(): void {
    this.accessPartyId = shared.getAccessPartyId();
  }

  setGarUser(): void {
    const { agent, accessPartyId, accessPartyList } = shared.getCoreData();
    if (process.env.NODE_ENV === 'development') {
      this.rootStore.staff.setStaffType(accessPartyId);
    }
    if (!isNil(accessPartyId) && accessPartyId !== '') {
      this.agent = agent;
      this.accessPartyId = accessPartyId;
      this.accessPartyIdList = accessPartyList;
      this.rootStore.staff.setStaffType(accessPartyId);
    }
  }
}
