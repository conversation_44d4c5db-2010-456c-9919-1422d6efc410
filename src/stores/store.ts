// rootStore.ts
import { makeAutoObservable } from 'mobx';

import { UnifiedStores } from './unifiedStores';
import { StaffStores } from './staffStores';
import { LoadingStores } from './loadingStores';
import { FormStores } from './formStores';
import { InfoStore } from './infoStores';

export class RootStore {
  unified: UnifiedStores;
  staff: StaffStores;
  loading: LoadingStores;
  form: FormStores;
  info: InfoStore;

  constructor() {
    // mark the sub-store properties as observables automatically
    makeAutoObservable<this, 'unified' | 'staff' | 'loading' | 'form' | 'info'>(
      this,
      {
        unified: false, // we don’t want MobX to recurse into the sub-store constructors
        staff: false,
        loading: false,
        form: false,
        info: false,
      },
    );

    // instantiate each store, passing `this` so they can refer back
    this.unified = new UnifiedStores(this);
    this.staff = new StaffStores(this);
    this.loading = new LoadingStores(this);
    this.form = new FormStores(this);
    this.info = new InfoStore(this);
  }
}

// create a single shared instance
export const rootStore = new RootStore();
export type IRootStore = typeof rootStore;
