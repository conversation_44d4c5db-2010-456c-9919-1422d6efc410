import { makeAutoObservable } from 'mobx';

export interface ILoadingStores {
  pageLoading: boolean;
  setPageLoading: (isLoading: boolean) => void;
}

export class LoadingStores implements ILoadingStores {
  pageLoading = false; // 页面loading
  rootStore: any;

  constructor(rootStore) {
    makeAutoObservable(this, {}, { autoBind: true });
    this.rootStore = rootStore;
  }

  setPageLoading(isLoading: boolean): void {
    this.pageLoading = isLoading;
  }
}
