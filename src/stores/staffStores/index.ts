import { makeAutoObservable } from 'mobx';
import { shared } from '@ies/united-sdk-i18n';
import { PERMS_FUNC_MAP } from '@common/constants/property';
import { PermissionPoint } from '@http_idl/infoService';

export interface IStaffStores {
  staffInfoType: PermissionPoint;
  staffAPIType: PermissionPoint;
  setStaffInfoType: (accessPartyId: string) => void;
  setStaffApiType: (accessPartyId: string) => void;
  setStaffType: (accessPartyId: string) => void;
}

export class StaffStores implements IStaffStores {
  staffInfoType: PermissionPoint = PermissionPoint.Operate; // INFO管理人员权限
  staffAPIType: PermissionPoint = PermissionPoint.Operate; // API管理人员权限
  rootStore: any;

  constructor(rootStore) {
    makeAutoObservable(this, {}, { autoBind: true });
    this.rootStore = rootStore;
  }

  setStaffInfoType(accessPartyId: string): void {
    if (
      shared.judgeAccessPartyRights(PERMS_FUNC_MAP.INFO_MANAGER, accessPartyId)
    ) {
      this.staffInfoType = PermissionPoint.Development;
    } else if (
      shared.judgeAccessPartyRights(PERMS_FUNC_MAP.INFO_EDITOR, accessPartyId)
    ) {
      this.staffInfoType = PermissionPoint.Product;
    } else {
      this.staffInfoType = PermissionPoint.Operate;
    }
  }

  setStaffApiType(accessPartyId: string): void {
    if (
      shared.judgeAccessPartyRights(PERMS_FUNC_MAP.API_MANAGER, accessPartyId)
    ) {
      this.staffAPIType = PermissionPoint.Development;
    } else if (
      shared.judgeAccessPartyRights(PERMS_FUNC_MAP.API_EDITOR, accessPartyId)
    ) {
      this.staffAPIType = PermissionPoint.Product;
    } else {
      this.staffAPIType = PermissionPoint.Operate;
    }
  }

  setStaffType(accessPartyId: string): void {
    this.setStaffApiType(accessPartyId);
    this.setStaffInfoType(accessPartyId);
  }
}
