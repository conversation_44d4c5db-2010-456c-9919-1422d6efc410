import * as React from 'react';
import { useState } from 'react';
import { <PERSON><PERSON>erRouter } from '@edenx/runtime/router-v5';
import { Layout, ConfigProvider, Spin } from '@hi-design/ui';
import AppNav from '@components/layout/nav';
import AppRouter from './router';
import UseAuthHooks from './hooks/useAuthHooks';
import { useStores } from './stores';
import { useDidMount } from '@common/utils/hook';
// eslint-disable-next-line @typescript-eslint/naming-convention
import en_US from '@hi-design/ui/es/components/locale/source/en_US';
// eslint-disable-next-line @typescript-eslint/naming-convention
import zh_CN from '@hi-design/ui/es/components/locale/source/zh_CN';
import { defaultLocale } from './sdks/i18n';
import { observer } from 'mobx-react';
import { confirm } from '@components/SaveConfirm';
import { initAIS } from './sdks/ais';

const { Sider, Content } = Layout;

interface APPProps {
  setLocale: () => void;
  locale: string;
  basename: string;
  name: string;
}

function App(props): React.FunctionComponentElement<APPProps> {
  const supportsHistory = 'pushState' in window.history;
  const { basename } = props;
  const { unified, loading } = useStores();

  useDidMount(() => {
    unified?.setGarUser();
    initAIS();
  });
  const [locales] = useState({ zh: zh_CN, en: en_US });
  const locale = defaultLocale as 'zh' | 'en';

  return (
    <Spin spinning={loading.pageLoading} size="large">
      <ConfigProvider locale={locales[locale]}>
        <BrowserRouter
          basename={basename}
          forceRefresh={!supportsHistory}
          getUserConfirmation={confirm}
        >
          <Layout style={{ height: '100vh' }}>
            <Layout>
              {!window.__PROWER_BY_GAR__ ? (
                <Sider>
                  <AppNav />
                </Sider>
              ) : null}
              <Content>
                <UseAuthHooks>
                  <AppRouter />
                </UseAuthHooks>
              </Content>
            </Layout>
          </Layout>
        </BrowserRouter>
      </ConfigProvider>
    </Spin>
  );
}

export default observer(App);
