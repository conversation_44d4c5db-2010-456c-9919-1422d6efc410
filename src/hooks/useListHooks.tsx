import { Target } from '@http_idl/infoService';
import { useState } from 'react';
import { useHistory } from '@edenx/runtime/router-v5';
interface IJumpDetail {
  target: Target;
  id?: string;
}
const useListHooks = () => {
  const [recordsVisible, setRecordsVisible] = useState<boolean>(false);
  const history = useHistory();
  const [formVisible, setFormVisible] = useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [dataId, setDataId] = useState<string>(null);
  const [title, setTitle] = useState('');
  const onRecordsCancel = (): void => {
    setRecordsVisible(false);
    setDataId(null);
  };
  const onRecordsShow = (id: string): void => {
    setRecordsVisible(true);
    setDataId(id);
  };
  const onFormCancel = (): void => {
    setFormVisible(false);
  };
  const onFormShow = (
    edit?: boolean,
    id?: string,
    titleProps?: string,
  ): void => {
    setFormVisible(true);
    setIsEdit(edit);
    setDataId(id);
    setTitle(titleProps);
  };
  const onJumpFormDetail = (props: IJumpDetail): void => {
    const { id, target } = props || {};
    const urlPrefix = target === Target.Info ? '/info/detail' : '/api/detail';
    const queryData = id ? `id=${id}` : '';
    history.push(`${urlPrefix}?${queryData}`);
  };
  return {
    recordsVisible,
    setRecordsVisible,
    onRecordsShow,
    onRecordsCancel,

    formVisible,
    setFormVisible,
    onFormShow,
    onFormCancel,
    onJumpFormDetail,

    isEdit,
    dataId,
    title,
  };
};

export default useListHooks;
