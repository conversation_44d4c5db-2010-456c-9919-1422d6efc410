/* eslint-disable max-lines-per-function */
/* eslint-disable max-lines */
import {
  API_FE_TEXT_MAP,
  COMMON_FE_TEXT_MAP,
  INFO_FE_TEXT_MAP,
  TranslateButtonType,
} from '@common/constants/I18nTextMap';
import { StatusType } from '@common/constants/listConfig';
import { formatNilValueToString } from '@common/utils/format';
import {
  genKey,
  getFieldType,
  getValuesStarling,
} from '@common/utils/starlingKey';
import {
  infoServiceClient,
  PermissionPoint,
  Target,
  TransType,
} from '@http_idl/infoService';
import { Modal, Toast } from '@hi-design/ui';
import { shared } from '@ies/united-sdk-i18n';
import useGetCustomerForm from './useCustomerForm';
import { isArray, isNil } from 'lodash-es';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useHistory } from '@edenx/runtime/router-v5';
import { cspTranslateGen } from '@ies/csp-translate-gen-semi2';
import { isOverseas } from '@utils/env';
import { sendCustomPerfMetric } from '@sdks/slardar';
import { useStores } from '@stores/index';
export interface IWindowFormState {
  target: Target;
  permission: PermissionPoint;
  id?: string;
  onBack: () => void;
}

export interface StarlingMapType {
  projectId?: string;
  apiKey?: string;
  namespaceIdToB?: string;
  namespaceNameToB?: string;
  namespaceIdToC?: string;
  namespaceNameToC?: string;
  userLangList?: any;
}
// 啊。。。。这里是谁写的，怎么不知道拆分呀，这么乱，状态怎么这么多，还能捋清嘛
// 啊。。。。原来是我自己，那还行。
export const useWindowFormState = (props: IWindowFormState) => {
  const { id, target, permission, onBack } = props || {};
  const isEdit = !isNil(id);
  const isI18n = isOverseas();
  const history = useHistory();
  const { agent } = shared.getCoreData();
  const [formTitle, setFormTitle] = useState('');
  const [formId, setFormId] = useState(id);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [isView, setIsView] = useState(false);
  const [isFormChange, setIsFormChange] = useState(false);
  // 用于存储该条INFO的starling数据，包括项目ID，空间ID，空间名称，apiKey
  const [initStarlingMap, setInitStarlingMap] = useState<StarlingMapType>({});
  const { form } = useStores();

  const starlingMapRef = useRef<StarlingMapType>({}); // 用于存储该条INFO的starling数据，包括项目ID，空间ID，空间名称，apiKey
  const toCSchemaMapRef = useRef(null); // 用于存储要翻译的TOC字段，不用useState是因为异步在sdk中拿不到最新的值
  const toBSchemaMapRef = useRef(null); // 用于存储要翻译的TOB字段
  const SubmitValueRef = useRef(null); // 用于存储用户已经输入的文案，在预览态由于编辑表单被隐藏，formRef值会丢失
  const formApiRef = useRef(null); // 编辑表单ref
  const viewFormApiRef = useRef(null); // 预览表单Ref
  const transDataRef = useRef(null); // 存储翻译完成任务

  const isInitRef = useRef(true); // 用于提交性能埋点

  const {
    formStatus, // 表单状态， 已启用/已禁用/配置中
    formSchema, // 编辑态表单schema
    viewSchema, // 预览态表单，在编辑态表单的基础上全部禁用编辑
    initValues, // 初始值
    loading,
    changeValueList,
    filedMap, // 记录那些是TOC字段，哪些是TOB字段
  } = useGetCustomerForm({
    isEdit,
    target,
    permission,
    id,
    starlingMapRef,
    setInitStarlingMap,
  });

  const initValue = useMemo(() => {
    if (SubmitValueRef.current) {
      return SubmitValueRef.current;
    }
    return initValues;
  }, [SubmitValueRef?.current, initValues]);

  /**
   * 回退列表页面，当表单被改动，需要弹窗确定
   */
  const handleOnBack = useCallback(() => {
    if (!isFormChange) {
      // 避免出现两次弹窗
      setIsFormChange(false);
      setTimeout(() => {
        onBack?.();
      }, 0);
    } else {
      Modal.warning({
        title: `${COMMON_FE_TEXT_MAP.Confirm_Quit?.(formTitle)}`,
        content: COMMON_FE_TEXT_MAP.Quit_Tips,
        okText: COMMON_FE_TEXT_MAP.Quit,
        cancelText: COMMON_FE_TEXT_MAP.Cancel,
        onOk: () => {
          setIsFormChange(false);
          setTimeout(() => {
            onBack?.();
          }, 0);
        },
      });
    }
  }, [onBack, isFormChange]);

  /**
   * 发布提交表单数据
   */
  const handleSubmit = useCallback(() => {
    // 不使用await的原因是需要将错误的字段滚动定位提示
    // 由于表单引擎问题，无法自动滚动定位，使用catch方法手动滚动
    const api = isView ? viewFormApiRef : formApiRef;
    api?.current
      ?.validate()
      .then(async (values: any) => {
        // 过滤提交的参数，将为null或undefined的内容（注意是清空下拉选项部分）置为空字符
        let value = {} as any;
        if (isView) {
          value = SubmitValueRef.current;
        } else {
          value = values;
        }
        setSubmitLoading(true);
        // 将对象中所有value为null或undefinedkey，全部置为空字符串
        formatNilValueToString(value);

        if (isI18n) {
          // const isHaveNotFullPushText = await checkOverseasTranslation({
          //   fieldMap: filedMap,
          //   value: value,
          //   starlingMapRef: starlingMapRef,
          //   setToastError: Toast.error
          // });
          // if (isHaveNotFullPushText) {
          //   setSubmitLoading?.(false);
          //   return;
          // }
          value.starlingMap = starlingMapRef?.current || {};
          if (
            target === Target.Info &&
            isArray(value?.enumMap) &&
            value?.enumMap?.length > 0 &&
            starlingMapRef?.current?.namespaceIdToC
          ) {
            value?.enumMap?.map(item => {
              item.key = genKey(
                item?.label,
                starlingMapRef?.current?.namespaceIdToC,
              );
              return item;
            });
          }
        }
        const toastTitle = value?.name as string;
        const params = {
          target: Number(target),
          newData: JSON.stringify(value),
          permissionPoint: permission,
          operateUserMail: `${agent?.Email || agent?.email},${agent?.UserName || agent?.username}`,
          ...(isEdit && { dataId: formId }),
        };
        const { failRecordList, BaseResp } =
          await infoServiceClient.UpdateOrAddData(params);
        if (BaseResp?.StatusCode === 0) {
          setIsFormChange(false);
          const statusName =
            formStatus === StatusType.INCONFIG
              ? COMMON_FE_TEXT_MAP.Create
              : COMMON_FE_TEXT_MAP.Update;
          Toast.success(
            COMMON_FE_TEXT_MAP.Enable_Success(toastTitle, statusName),
          );
          const urlPrefix = target === Target.Info ? '/info/list' : '/api/list';
          history.replace(`${urlPrefix}`);
          setSubmitLoading(false);
          return;
        }
        if (isArray(failRecordList)) {
          const scrollField = failRecordList[0]?.failedKey;
          // 证明某些字段校验不通过，需要展示
          failRecordList?.map(item => {
            const { failedKey, failedDesc } = item || {};
            api?.current?.setError(failedKey, failedDesc);
          });
          // 滚动到第一个错误展示的位置
          api?.current?.scrollToField(scrollField);
        } else {
          Toast.error(BaseResp?.StatusMessage || COMMON_FE_TEXT_MAP.Fail);
        }
        setSubmitLoading(false);
      })
      .catch((errorValues: any) => {
        console.error(errorValues);
        setSubmitLoading(false);
        api?.current?.scrollToField(Object.keys(errorValues)[0]);
      });
  }, [
    target,
    agent,
    permission,
    isView,
    isEdit,
    formStatus,
    formId,
    filedMap,
    isI18n,
  ]);

  /**
   * 保存提交表单数据，保存无需数据校验与翻译校验
   */
  const handleSave = useCallback(() => {
    if (formStatus !== StatusType.INCONFIG) {
      // 只有配置中状态的INFO/API才可以保存
      return;
    }
    // 保存只需要进行表单必填与规则校验即可，无需进行后端校验
    const api = isView ? viewFormApiRef : formApiRef;
    api?.current
      ?.validate()
      .then(async (values: any) => {
        setSaveLoading(true);
        let value = {} as any;
        if (isView) {
          value = SubmitValueRef?.current;
        } else {
          value = values;
        }
        formatNilValueToString(value);
        if (isI18n) {
          // 保存时需要将projectId, apiKey, nameSpaceId, namespaceName这些内容给后端保存起来
          value.starlingMap = starlingMapRef?.current || {};
        }
        const toastTitle = value?.name as string;
        const params = {
          data: JSON.stringify(value),
          permissionPoint: permission,
          operateUserMail: `${agent?.Email || agent?.email},${agent?.UserName || agent?.username}`,
          ...(formId && target === Target.Info && { infoId: formId }),
          ...(formId && target === Target.Api && { apiId: formId }),
        };
        try {
          let result: any = {};
          if (target === Target.Info) {
            result = await infoServiceClient.SaveInfo(params);
          }
          if (target === Target.Api) {
            result = await infoServiceClient.SaveApi(params);
          }
          const { BaseResp, infoId: SaveInfoId, apiId: SaveApiId } = result;
          const resultId = SaveInfoId || SaveApiId;
          if (BaseResp?.StatusCode === 0) {
            setIsFormChange(false);
            Toast.success(
              `「${toastTitle}」${COMMON_FE_TEXT_MAP.Save_Success}`,
            );
            if (formId !== resultId) {
              const urlPrefix =
                target === Target.Info ? '/info/detail' : '/api/detail';
              history.push(`${urlPrefix}?id=${resultId}`);
              // 自定义组件获取不到最新value值，需要强制刷新一下
              window.location.reload();
            } else {
              setFormId(resultId);
              setFormTitle(COMMON_FE_TEXT_MAP.Edit_Name(toastTitle));
            }
          } else {
            Toast.error(
              BaseResp?.StatusMessage ||
                `「${toastTitle}」${COMMON_FE_TEXT_MAP.Save_Fail}`,
            );
          }
          setSaveLoading(false);
        } catch (e) {
          setSaveLoading(false);
          Toast.error(`「${toastTitle}」${COMMON_FE_TEXT_MAP.Save_Fail}`);
        }
      })
      .catch((errorValues: any) => {
        setSaveLoading(false);
        formApiRef?.current?.scrollToField(Object.keys(errorValues)[0]);
      });
  }, [target, agent, isView, permission, formId, formStatus, isI18n]);

  /**
   * 编辑表单，用于控制部分依赖字段数据展示
   */
  const handleOnValueChange = useCallback(
    (_values, field) => {
      if (!isFormChange) {
        setIsFormChange(true);
      }
      // 将schema中存在依赖字段进行一个数据控制清除，当A字段改变时，会引起B字段的数据清空
      changeValueList.forEach(item => {
        const { key, effectInfo = [] } = item || {};
        if (Object.keys(field)[0] === key) {
          effectInfo.forEach(i => {
            formApiRef?.current?.setValue(i?.key, i?.value);
          });
        }
      });
      form?.setFormValues(_values);
    },
    [isFormChange, changeValueList, form],
  );

  /**
   * 点击唤起翻译组件回调函数，用于存储当前用户已输入的文案，生成最新的待翻译schema
   */
  const handleOnBeforeClickTranslation = useCallback(async () => {
    const { isHaveAgent, isHaveUser } = getFieldType(filedMap);
    let values = {};
    if (isView) {
      formApiRef?.current?.setValues(SubmitValueRef.current);
      values = SubmitValueRef.current;
    } else {
      SubmitValueRef.current = formApiRef?.current?.getValues();
      values = formApiRef?.current?.getValues();
    }
    const userSchema = getValuesStarling(
      filedMap.toC,
      values,
      starlingMapRef?.current?.namespaceIdToC,
    )?.schema;
    const agentSchema = getValuesStarling(
      filedMap.toB,
      values,
      starlingMapRef?.current?.namespaceIdToB,
    )?.schema;
    const refreshSchemaConfig = {} as any;
    if (isHaveAgent) {
      refreshSchemaConfig.agentSchema = agentSchema;
    }
    if (isHaveUser) {
      refreshSchemaConfig.userSchema = userSchema;
    }
    cspTranslateGen.refreshSchema(refreshSchemaConfig);
    toCSchemaMapRef.current = getValuesStarling(
      filedMap.toC,
      values,
      starlingMapRef?.current?.namespaceIdToC,
    );
    toBSchemaMapRef.current = getValuesStarling(
      filedMap.toB,
      values,
      starlingMapRef?.current?.namespaceIdToB,
    );
    // 唤起时获取所有已翻译完成文案
    const detail = {} as any;
    if (isHaveAgent) {
      const getAgentTranslateStatus =
        await cspTranslateGen.getAgentTranslateStatus();
      detail.agent = getAgentTranslateStatus?.data?.langReleasedDetail;
    }
    if (isHaveUser) {
      const getUserTranslateStatus =
        await cspTranslateGen.getUserTranslateStatus();
      detail.user = getUserTranslateStatus?.data?.langReleasedDetail;
    }
    transDataRef.current = detail;
  }, [isView, target, formApiRef, filedMap, starlingMapRef]);

  /**
   * 点击翻译组件，切换语言列表，当非默认语言时，表单为预览态，且展示翻译后文案
   */
  const handleOnClickChangeLang = useCallback(
    (langData: any, buttonType: TranslateButtonType) => {
      const { data, type } = langData || {};
      if (type === 'add_lang') {
        starlingMapRef.current.userLangList = data;
        return;
      }
      if (type === 'cancel_sidesheet') {
        // 关闭抽屉也会触发onchange事件，但是不做处理
        return;
      }
      if (type === 'click_lang') {
        if (data === 'origin') {
          // 默认语言才可以编辑
          setIsView(false);
          setTimeout(() => {
            formApiRef?.current?.setValues(SubmitValueRef.current);
          }, 0);
          return;
        }
        // 点击了其他语言，表单处于预览状态，不可以编辑内容
        setIsView(true);
        // 拿到输入的文案，进行文案预览展示
        // 获取当前点击的语言，以及该语言下全量发布的所有文案
        let langDetail = [];
        let listMap = {};
        if (buttonType === TranslateButtonType.AGENT) {
          langDetail = transDataRef?.current?.agent?.[data] || [];
          listMap = toBSchemaMapRef?.current?.listMap || {};
        }
        if (buttonType === TranslateButtonType.USER) {
          langDetail = transDataRef?.current?.user?.[data] || [];
          listMap = toCSchemaMapRef?.current?.listMap || {};
        }
        setTimeout(() => {
          Object.keys(listMap).forEach(item => {
            const targetContent = langDetail?.filter(
              lang => lang?.key === listMap[item]?.strKey,
            )?.[0]?.targetContent;
            // 如果存在更新文案，但是还没有全量发布翻译，使用已输入文案
            if (targetContent && viewFormApiRef?.current) {
              viewFormApiRef?.current?.setValue(item, targetContent);
            }
          });
        }, 0);
        // 将语言转换成对应文案内容
      }
    },
    [formApiRef, viewFormApiRef, SubmitValueRef, transDataRef],
  );

  /**
   * 初始化i18n组件后的回调函数
   */
  const handleStarlingChange = (
    data: any,
    type: TransType,
    name: string,
  ): void => {
    const { namespaceId } = data || {};
    if (type === TransType.ToC && namespaceId && name) {
      starlingMapRef.current.namespaceIdToC = namespaceId;
      starlingMapRef.current.namespaceNameToC = name;
    }
    if (type === TransType.ToB && namespaceId && name) {
      starlingMapRef.current.namespaceIdToB = namespaceId;
      starlingMapRef.current.namespaceNameToB = name;
    }
  };

  useEffect(() => {
    let title = '';
    if (isEdit) {
      title = COMMON_FE_TEXT_MAP.Edit_Name(initValues?.name || '-');
    } else {
      title =
        target === Target.Info
          ? INFO_FE_TEXT_MAP.Create
          : API_FE_TEXT_MAP.Create;
    }
    setFormTitle(title);
  }, [initValues?.name, isEdit, target]);

  useEffect(() => {
    setFormId(id);
  }, [id]);

  useEffect(() => {
    if (!loading && isInitRef.current) {
      isInitRef.current = false;
      sendCustomPerfMetric({
        target,
        page: 'detail',
      });
    }
  }, [loading, target]);

  useEffect(
    () => () => {
      form?.setApiParams(null);
      form?.setApiTestResult({});
    },
    [],
  );

  return {
    formTitle,
    formId,
    initStarlingMap,
    filedMap,
    formSchema,
    viewSchema,
    formStatus,
    initValue,

    loading,
    submitLoading,
    saveLoading,

    isView,
    isFormChange,

    SubmitValueRef,
    formApiRef,
    viewFormApiRef,
    starlingMapRef,

    handleOnBack,
    handleSubmit,
    handleSave,
    handleOnValueChange,
    handleOnBeforeClickTranslation,
    handleOnClickChangeLang,
    handleStarlingChange,
  };
};
