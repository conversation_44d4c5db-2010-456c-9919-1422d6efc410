import { useCallback, useState } from 'react';

interface IUseVisibleState {
  defaultVisible?: boolean;
}
export const useVisibleState = (props: IUseVisibleState) => {
  const { defaultVisible = false } = props || {};
  const [visible, setVisible] = useState<boolean>(defaultVisible);

  const onShow = useCallback(() => {
    setVisible(true);
  }, []);
  const onCancel = useCallback(() => {
    setVisible(false);
  }, []);

  return {
    visible,
    onShow,
    onCancel,
  };
};
