/**
 * 功能：根据人员权限，INFO/API，infoID（新建没有），通过接口获取schema值，并解析生成schema，defaultValue，effectValueList
 * @param permission 人员权限
 * @param target INFO/API
 * @param isEdit 是否是编辑页
 * @param id 数据ID
 *
 * @state defaultValue 是一个key value的对象值，通过获取schema中的数据，来生成默认value值，传给表单库
 * @state schema 通过解析后端返回的约定schema格式，生成表单库可用的schema
 * @state loading 控制表蛋页loading效果
 * @state changeValueList 用于控制某个字段改变时，引起的其他字段内容清空
 *
 * @function transferFormSchema 解析schema
 */
import { MutableRefObject, useEffect, useState } from 'react';
import {
  infoServiceClient,
  PermissionPoint,
  Target,
} from '@http_idl/infoService';
import { cloneDeep, isBoolean, isNil } from 'lodash-es';
import {
  transferFormSchema,
  transferViewSchema,
} from '@common/utils/transferFormSchema';
import { Toast } from '@hi-design/ui';
import { ManageType } from '@common/utils/starlingKey';
import { StatusType } from '@common/constants/listConfig';
import { FormFieldMap } from '@common/constants/formSchema';
import { getStarlingProjectInfo } from '@common/utils/overseas';
import { StarlingMapType } from './useWindowFormState';
import { useStores } from '@stores/index';
interface IUseGetCustomerForm {
  permission: PermissionPoint;
  target: Target;
  isEdit: boolean;
  id?: string;
  starlingMapRef?: MutableRefObject<StarlingMapType>;
  setInitStarlingMap?: (data: any) => void;
}

const useGetCustomerForm = (props: IUseGetCustomerForm): any => {
  const { permission, target, id, isEdit, starlingMapRef, setInitStarlingMap } =
    props;
  const { form } = useStores();
  const [initValues, setInitValues] = useState({});
  const [formSchema, setFormSchema] = useState([]);
  const [viewSchema, setViewSchema] = useState([]);
  const [loading, setLoading] = useState(true);
  const [changeValueList, setChangeValueList] = useState([]);
  const [formStatus, setFormStatus] = useState(StatusType.INCONFIG);
  const [filedMap, setFieldMap] = useState<FormFieldMap>({
    toC: [],
    toB: [],
  });
  const fetchDetail = async (): Promise<void> => {
    setLoading(true);
    try {
      const params = {
        permissionPoint: permission,
        target,
        ...(!isNil(id) && { dataId: id }),
      };
      const manageType =
        target === Target.Info ? ManageType.INFO : ManageType.API;

      const records = await infoServiceClient.GetInsertOrUpdateSchema(params);
      await getStarlingProjectInfo({
        records,
        starlingMapRef,
        setInitStarlingMap,
      });
      const { schema, value, effectValueList, isSuccess, fieldMap } =
        transferFormSchema({
          schema: records?.schema || '',
          isEdit,
          manageType,
        });
      if (!isSuccess) {
        Toast.error('transform schema error');
      }
      setFieldMap(fieldMap);
      // 新建是配置中，如果是接口返回出错导致没有状态，隐藏按钮
      setFormStatus(isNil(id) ? StatusType.INCONFIG : records?.status);
      setFormSchema(schema);
      setViewSchema(transferViewSchema(cloneDeep(schema)));
      setInitValues(value);
      form?.setFormValues(value);
      setChangeValueList(effectValueList);
    } catch (err) {
      Toast.error('schema error');
    }
    setLoading(false);
  };

  useEffect(() => {
    if (permission && target && isBoolean(isEdit)) {
      fetchDetail();
    }
  }, [permission, target, isEdit]);

  return {
    starlingMapRef,
    initValues,
    loading,
    formStatus,
    formSchema,
    viewSchema,
    changeValueList,
    filedMap,
  };
};

export default useGetCustomerForm;
