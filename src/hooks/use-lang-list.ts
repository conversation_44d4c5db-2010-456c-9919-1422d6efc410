import { useState, useEffect } from 'react';
import { getTCC } from '@/sdks/ais';

interface LangList {
  required: string[];
  optional: string[];
}

interface UseLangListResult extends LangList {
  loading: boolean;
  error: Error | null;
}

// module-scope cache for our single TCC call
let cachedLangList: LangList | null = null;

export function useLangList(): UseLangListResult {
  const [required, setRequired] = useState<string[]>([]);
  const [optional, setOptional] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isCancelled = false;

    // if cached, short-circuit
    if (cachedLangList) {
      setRequired(cachedLangList.required);
      setOptional(cachedLangList.optional);
      setLoading(false);
      return;
    }

    // fetch once and cache
    async function fetchList() {
      try {
        const res = await getTCC('country_lang', 'ies.csp.ais');
        const info = res.data?.project?.info ?? {};
        const { required = [], optional = [] } = info;

        if (!isCancelled) {
          setRequired(required);
          setOptional(optional);
          cachedLangList = { required, optional };
        }
      } catch (err: any) {
        if (!isCancelled) {
          setError(err);
        }
      } finally {
        if (!isCancelled) {
          setLoading(false);
        }
      }
    }

    fetchList();
    return () => {
      isCancelled = true;
    };
  }, []);

  return { required, optional, loading, error };
}
