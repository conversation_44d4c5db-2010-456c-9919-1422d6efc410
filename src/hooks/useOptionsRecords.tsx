/**
 * 功能：用于存储操作记录页面多个状态值，方便代码维护
 *
 * @ref scrollRef 用于时间切换后，列表恢复到第一页
 * @state queryTime 打开抽屉时的时间，用于后端限定返回时间limit
 * @state recordList 操作记录列表
 * @state loading 抽屉页面的loading效果
 * @state listLoading 下滑加载更多的loading效果
 * @state hasMore 是否有更多，即是否还会触发下滑加载更多
 * @state count pageNum页数
 * @state filterInfo 筛选参数
 */

import { OperateRecord } from '@http_idl/infoService';
import { useRef, useState } from 'react';

interface State {
  scrollRef: any;
  queryTime: string;
  recordList: Array<OperateRecord>;
  loading: boolean;
  listLoading: boolean;
  hasMore: boolean;
  count: number;
  filterInfo: any;
  setRecordList: (e: any) => void;
  setLoading: (e: boolean) => void;
  setListLoading: (e: boolean) => void;
  setHasMore: (e: boolean) => void;
  setCount: (e: number) => void;
  setFilterInfo: (e: any) => void;
}

const useOptionsRecords = (): State => {
  const scrollRef = useRef(null);
  const [recordList, setRecordList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [listLoading, setListLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [count, setCount] = useState(1);
  const [filterInfo, setFilterInfo] = useState({});
  const [queryTime] = useState(String(new Date().getTime()));

  return {
    scrollRef,
    queryTime,
    recordList,
    loading,
    listLoading,
    hasMore,
    count,
    filterInfo,
    setRecordList,
    setLoading,
    setListLoading,
    setHasMore,
    setCount,
    setFilterInfo,
  };
};

export default useOptionsRecords;
