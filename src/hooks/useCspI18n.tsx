import { FormFieldMap } from '@common/constants/formSchema';
import { getFieldType } from '@common/utils/starlingKey';
import { Target, TransType } from '@http_idl/infoService';
import { cspTranslateGen } from '@ies/csp-translate-gen-semi2';
import { shared } from '@ies/united-sdk-i18n';
import { isOverseas } from '@utils/env';
import { isEmpty, isObject } from 'lodash-es';
import { useEffect } from 'react';
import { StarlingMapType } from './useWindowFormState';
import { useLangList } from './use-lang-list';

interface UseCspI18nType {
  loading: boolean;
  target: Target;
  fieldMap: FormFieldMap;
  initStarlingMap: StarlingMapType;
  handleStarlingChange: (data: any, type: TransType, name: string) => void;
}
export const useCspI18n = (props: UseCspI18nType) => {
  const accessPartyId = shared.getAccessPartyId();
  const { loading, fieldMap, initStarlingMap, target, handleStarlingChange } =
    props || {};

  const {
    required,
    optional,
    loading: langListLoading,
    error: langListError,
  } = useLangList();

  useEffect(() => {
    if (!loading && accessPartyId && isOverseas() && !langListLoading) {
      if (langListError) {
        console.error('Failed to get language list from AIS: ', langListError);
      }
      // 确保starlingMap返回回来了
      const {
        namespaceIdToB,
        namespaceNameToB,
        namespaceIdToC,
        namespaceNameToC,
        projectId,
        userLangList,
      } = initStarlingMap || ({} satisfies StarlingMapType);
      if (!projectId) {
        return;
      }
      const dateTime = new Date().getTime();
      const { isHaveAgent, isHaveUser } = getFieldType(fieldMap);
      const cspI18nLangConfig = {} as any;
      const initLang = isObject(userLangList)
        ? userLangList
        : { required, optional };

      if (namespaceIdToC || namespaceIdToB) {
        if (isHaveUser) {
          cspI18nLangConfig.user = {
            projectId,
            namespaceId: namespaceIdToC,
            namespaceName: namespaceNameToC,
            initLang,
            onStarlingChange: (data: any) =>
              handleStarlingChange?.(data, TransType.ToC, namespaceNameToC),
          };
        }
        if (isHaveAgent) {
          cspI18nLangConfig.agent = {
            projectId,
            namespaceId: namespaceIdToB,
            namespaceName: namespaceNameToB,
            onStarlingChange: (data: any) =>
              handleStarlingChange?.(data, TransType.ToB, namespaceNameToC),
          };
        }
      } else {
        if (isHaveUser) {
          cspI18nLangConfig.user = {
            projectId,
            namespaceName: `${target === Target.Info ? 'INFO' : 'API'}-${accessPartyId}-${dateTime}-ToC`,
            initLang: { required, optional },
            onStarlingChange: (data: any) =>
              handleStarlingChange?.(
                data,
                TransType.ToC,
                `API-${accessPartyId}-${dateTime}-ToC`,
              ),
          };
        }
        if (isHaveAgent) {
          cspI18nLangConfig.agent = {
            projectId,
            namespaceName: `${target === Target.Info ? 'INFO' : 'API'}-${accessPartyId}-${dateTime}-ToB`,
            onStarlingChange: (data: any) =>
              handleStarlingChange?.(
                data,
                TransType.ToB,
                `API-${accessPartyId}-${dateTime}-ToB`,
              ),
          };
        }
      }
      if (!isEmpty(cspI18nLangConfig)) {
        cspTranslateGen.init(cspI18nLangConfig);
      }
    }
  }, [
    accessPartyId,
    fieldMap,
    initStarlingMap,
    langListError,
    langListLoading,
    loading,
    optional,
    required,
    target,
  ]);
};
