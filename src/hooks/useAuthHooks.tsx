import { FC } from 'react';
import NoAgentError from '../components/global/noAgentError';
import { isNil } from 'lodash-es';
import { shared } from '@ies/united-sdk-i18n';

interface UseAuthHooksProps {
  children: React.ReactElement;
}

const useAuthHooks: FC<UseAuthHooksProps> = props => {
  // 进行人员权限控制
  const { children } = props;
  const { agent } = shared.getCoreData();
  if (isNil(agent) || agent?.Status === 0) {
    // 没有拿到人员信息
    return <NoAgentError />;
  }
  return children;
};

export default useAuthHooks;
