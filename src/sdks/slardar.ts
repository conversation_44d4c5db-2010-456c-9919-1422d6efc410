import { Target } from '@http_idl/infoService';
import { shared } from '@ies/united-sdk-i18n';
import browserClient from '@slardar/web/maliva';
import { isBoe } from '@utils/env';

export const enum SlardarEventName {
  // 列表筛选完成时间
  PROCESS_LIST_SEARCH = 'process_list_search',

  /** 自定义首屏 */
  CUSTOM_FMP = 'custom_fmp',
}

// slardar 埋点时间统计
interface SlardarTImeLoggerType {
  eventName: SlardarEventName;
  duration: number;
  target?: Target; // 如果INFO/API都有的event，用category做区分
  otherConfig?: {
    [type: string]: any;
  };
}

const TARGET_EVENT_NAME = {
  [Target.Info]: 'INFO',
  [Target.Api]: 'API',
};

const slardarLoggerInfo = (
  eventName: string,
  params: Record<string, any> = {},
  metrics: Record<string, any> = {},
) => {
  try {
    browserClient('sendEvent', {
      name: eventName,
      metrics, // 具体统计p90的传参
      categories: {
        // 自定义传参
        ...params,
      },
    });
  } catch (e) {
    console.log('slardar event send error', e);
  }
};

export const slardarTimeLogger = (params: SlardarTImeLoggerType) => {
  try {
    const {
      eventName,
      duration = 0,
      target = '',
      otherConfig = {},
    } = params || {};
    const agent = window?.$unitedHelpdeskI18nInitData?.userInfo?.agent;
    if (!eventName) {
      return;
    }
    const infoParams = {
      ...otherConfig,
      category: TARGET_EVENT_NAME[target] || '-',
      duration,
      accessPartyId: shared?.getAccessPartyId() || '-',
      agent:
        agent?.Email ||
        agent?.email ||
        agent?.UserName ||
        agent?.username ||
        '-',
    };
    slardarLoggerInfo(eventName, infoParams, { duration });
  } catch (e) {
    console.error('slardar error', e);
  }
};

const getEnv = () => {
  if (process.env.NODE_ENV === 'production') {
    if (process.env.BUILD_TYPE === 'online') {
      if (isBoe) {
        return 'boe';
      } else {
        return 'production';
      }
    } else {
      return 'test';
    }
  } else {
    return 'localDev';
  }
};

export const slardarInit = () => {
  const snssdk = 'moc.kdssns'.split('').reverse().join('');
  browserClient('init', {
    bid: 'helpdesk_info_management_i18n',
    env: getEnv(),
    release:
      window?.gfdatav1?.version || process.env.BUILD_VERSION || '0.0.0.0',
    userId: window?.$unitedHelpdeskI18nInitData?.userInfo?.agent?.Email,
    plugins: {
      ajax: {
        ignoreUrls: [
          'sgali-mcs.byteoversea.com',
          'mon-va.byteoversea.com',
          `mcs.${snssdk}`,
        ],
      },
    },
  });
  browserClient('start');
};

interface SendCustomPerfMetricType {
  target: Target;
  page: 'list' | 'detail';
}
export const sendCustomPerfMetric = (
  params: SendCustomPerfMetricType,
): void => {
  try {
    const { target, page } = params || {};
    let duration = 0;
    const { customEventTrack } = window?.Garfish?.getGlobalObject() || {};
    const isFirstLoadByteHi = customEventTrack?.renderTimes === 1;
    if (isFirstLoadByteHi) {
      duration = Math.floor(performance.now());
    } else {
      duration = Math.floor(
        performance.now() - customEventTrack?.customPerformanceStart,
      );
    }
    browserClient('sendCustomPerfMetric', {
      value: duration,
      name: SlardarEventName.CUSTOM_FMP,
      isCustom: true,
      extra: {
        firstLoad: isFirstLoadByteHi ? '1' : '0',
        category: TARGET_EVENT_NAME[target] || '-',
        page: page || '-',
      },
    });
  } catch {
    console.error('send custom pre metric error');
  }
};
