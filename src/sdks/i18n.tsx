import { I18n } from '@ies/starling_intl';
import Starling from '@ies/starling_client';
import { registerReactInstance } from '@ies/intl-react-plugin';
import config from '@common/constants/config';
import zhLocaleStarling from '../locale/zh.json';
import enLocaleStarling from '../locale/en.json';
import { isEmpty } from 'lodash-es';

const defaultLocale = localStorage.getItem('unitedLang') || 'zh';

let localeStarling = {
  zh: zhLocaleStarling,
  en: enLocaleStarling,
};
const fetchLocaleDatas = async (): Promise<void> => {
  try {
    const starling = new Starling({
      ...config.starling,
      locale: defaultLocale,
    });
    await starling.load(texts => {
      localeStarling = {
        ...localeStarling,
        [defaultLocale]: !isEmpty(texts) ? texts : zhLocaleStarling,
      };
    });
  } catch (error) {
    console.error('setLocale err:', error);
  }
  try {
    I18n.use(registerReactInstance).init({
      lng: defaultLocale,
      fallbackLng: ['zh', 'en'],
      resources: {
        zh: {
          translation: localeStarling.zh,
        },
        en: {
          translation: localeStarling.en,
        },
      },
      thirdParamFallback: true,
    });
  } catch (error) {
    console.error('setLang err:', error);
  }
};
const setLocale = (lang: string): void => {
  localStorage.setItem('unitedLang', lang);
  window.location.reload();
};
export default I18n;
export { fetchLocaleDatas, setLocale, defaultLocale };
