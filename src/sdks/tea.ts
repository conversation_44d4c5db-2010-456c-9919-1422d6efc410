import Tea from '@dp/byted-tea-sdk-oversea';
import config from '@common/constants/config';

Tea.init(config.tea);

export default {
  setUser(userName: string): void {
    Tea.config({
      user_unique_id: userName,
      log: true,
    });
    Tea.start();
  },
  pageView({ module, page, label }): void {
    Tea.event('page_view', {
      module,
      page,
      label,
    });
  },
  submit(): void {
    Tea.event('submit', {
      module,
    });
  },
};
