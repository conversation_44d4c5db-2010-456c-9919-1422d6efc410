import AIS from '@ies/ais';
import { GetJsonTCCResponse } from '@ies/ais/idl/http_idl/entry/fe';

export const initAIS = () => {
  AIS.init({
    psm: 'ies.helpdesk.info_management_i18n', // 待接入业务的 PSM（白名单控制访问）
  });
};

export const getTCC = async (
  key: string,
  serviceName?: string,
  confSpace?: string,
): Promise<{ data: Record<string, any> }> => {
  const result = (await AIS.fe.getJsonTCC({
    key,
    confSpace,
    serviceName: serviceName ?? 'ies.helpdesk.info_management_i18n',
  })) as GetJsonTCCResponse & { data?: Record<string, any> };
  if (result?.statusCode !== 0) {
    return Promise.reject(result);
  }
  return { data: result?.data || {} };
};
