import { FC, useEffect } from 'react';
import styles from './index.module.scss';
import { API_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { shared } from '@ies/united-sdk-i18n';
import { ACCESS_API_PARTY_KEY } from '@common/constants';
import ListContent from '@components/ListContent';
import { Target } from '@http_idl/infoService';
import PageHeader from '@/components/page-header';

const ApiManagement: FC = () => {
  useEffect(() => {
    const accessPartyId = shared.getAccessPartyId();
    localStorage.setItem(ACCESS_API_PARTY_KEY, accessPartyId!);
  }, [shared.getAccessPartyId()]);
  return (
    <div className={styles.page}>
      <PageHeader
        title={API_FE_TEXT_MAP.Title}
        description={API_FE_TEXT_MAP.Desc}
      />
      <div className={styles.content}>
        <ListContent target={Target.Api} />
      </div>
    </div>
  );
};

export default ApiManagement;
