import { useEffect, useRef, useState } from 'react';
import { useHistory } from '@edenx/runtime/router-v5';
import queryString from 'query-string';
import { useStores } from '@stores/index';
import { shared } from '@ies/united-sdk-i18n';
import { useWindowFormState } from '@hooks/useWindowFormState';
import { useCspI18n } from '@hooks/useCspI18n';
import { ACCESS_API_PARTY_KEY } from '@common/constants';
import { PermissionPoint, Target } from '@http_idl/infoService';
import { isNil } from 'lodash-es';

export const useApiDetailStateManage = () => {
  const history = useHistory();
  const { id } = queryString.parse(history.location.search) || ({} as any);
  const accessPartyId = shared.getAccessPartyId();
  const { staff } = useStores();
  const { staffAPIType } = staff || {};
  const topRef = useRef(null);
  const [topHeight, setTopHeight] = useState<number>(0);
  const {
    formSchema,
    viewSchema,
    initValue,
    formStatus,
    formTitle,
    initStarlingMap,
    filedMap,

    loading,
    submitLoading,
    saveLoading,
    isView,
    isFormChange,

    formApiRef,
    viewFormApiRef,

    handleSubmit,
    handleSave,
    handleOnBack,
    handleOnValueChange,
    handleOnBeforeClickTranslation,
    handleOnClickChangeLang,
    handleStarlingChange,
  } = useWindowFormState({
    target: Target.Api,
    id,
    permission: staffAPIType,
    onBack: () => history.push('/api/list'),
  });

  useCspI18n({
    loading,
    fieldMap: filedMap,
    initStarlingMap,
    target: Target.Api,
    handleStarlingChange,
  });

  useEffect(() => {
    if (!accessPartyId) {
      return;
    }
    const val = localStorage.getItem(ACCESS_API_PARTY_KEY);
    // 如果在编辑页切换接入方跳转到列表页
    if (val && accessPartyId !== val) {
      history.replace('/api/list');
    }
    localStorage.setItem(ACCESS_API_PARTY_KEY, accessPartyId);
  }, [accessPartyId, history]);

  useEffect(() => {
    setTopHeight(topRef?.current?.offsetHeight || 0);
  }, [loading, topRef?.current?.offsetHeight]);

  return {
    FormBaseParams: {
      loading,
      topRef,
      formTitle,
      formStatus,
      isChange: isFormChange,
      noPermission: staffAPIType === PermissionPoint.Operate,
      onBack: handleOnBack,
    },
    // 表单主体状态数据
    FormRenderParams: {
      formSchema, // 默认编辑表单Schema
      viewSchema, // 国际化预览状态下的Schema
      formApiRef, // 默认编辑表单API
      viewFormApiRef, // 国际化预览表单API
      isView, // 是否是预览态，是的话使用预览表单
      initValue, // 初始化默认值
      saveLoading, // 保存loading
      submitLoading, // 发布loading，发布时会有文字提示
      style: { height: `calc(100vh - 150px - ${topHeight}px)` },
      onValueChange: handleOnValueChange,
    },
    // 表单底部操作按钮
    FormFooterParams: {
      target: Target.Api,
      submitLoading,
      saveLoading,
      filedMap,
      status: formStatus,
      isEdit: !isNil(id),
      onBeforeTranslation: handleOnBeforeClickTranslation,
      onClickLang: handleOnClickChangeLang,
      onSubmit: handleSubmit,
      onSave: handleSave,
      onCancel: handleOnBack,
    },
  };
};
