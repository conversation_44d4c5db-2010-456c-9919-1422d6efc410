import { isEmpty } from 'lodash-es';
import SpinLoading from '@components/CustomerFormRender/SpinLoading';
import BackNav from '@components/WindowForm/BackNav';
import FormTitle from '@components/WindowForm/FormTitle';
import FormFooter from '@components/WindowForm/Footer';
import FormRender from '@components/WindowForm/FormRender';
import { observer } from 'mobx-react';
import {
  API_FE_TEXT_MAP,
  COMMON_FE_TEXT_MAP,
} from '@common/constants/I18nTextMap';
import NoAgentError from '@components/global/noAgentError';
import SchemaError from '@components/global/SchemaError';
import { useApiDetailStateManage } from './useStateManage';
import Style from './index.module.scss';
import { Prompt } from '@edenx/runtime/router-v5';

const APIDetail = () => {
  const { FormRenderParams, FormFooterParams, FormBaseParams } =
    useApiDetailStateManage();

  const {
    loading,
    formTitle,
    topRef,
    formStatus,
    noPermission,
    isChange,
    onBack,
  } = FormBaseParams || {};

  if (loading) {
    return (
      <div className={Style.LoadingBox}>
        <SpinLoading loading={loading} />
      </div>
    );
  }
  if (noPermission) {
    return <NoAgentError />;
  }
  if (isEmpty(FormRenderParams?.formSchema)) {
    return <SchemaError />;
  }
  return (
    <div className={Style.ApiContent}>
      <BackNav onBack={onBack} title={API_FE_TEXT_MAP.Back_Tips} />
      <div ref={topRef}>
        <FormTitle
          className={Style.ApiTitle}
          title={formTitle}
          status={formStatus}
        />
      </div>
      <FormRender {...FormRenderParams} />
      <FormFooter className={Style.WindowApiFormFooter} {...FormFooterParams} />
      <Prompt
        when={isChange}
        message={() => COMMON_FE_TEXT_MAP.Prompt_Message(formTitle)}
      />
    </div>
  );
};

export default observer(APIDetail);
