.LoadingBox {
  width: 100%;
  height: 100vh;
}
.InfoContent {
  margin: 0px auto;
  width: 650px;
  position: relative;
  display: flex;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  &Top {
    width: 100%;
    display: flex;
    margin-bottom: 40px;

    justify-content: flex-start;
    align-items: center;
    &<PERSON><PERSON> {
      margin-left: 20px;
    }
  }
}


.WindowInfoFormFooter {
  position: absolute;
  bottom: 0px;
}