import { useCallback, useEffect, useRef, useState } from 'react';
import { useHistory } from '@edenx/runtime/router-v5';
import queryString from 'query-string';
import { useStores } from '@stores/useStores';
import { shared } from '@ies/united-sdk-i18n';
import { useWindowFormState } from '@hooks/useWindowFormState';
import { useCspI18n } from '@hooks/useCspI18n';
import { ACCESS_INFO_PARTY_KEY } from '@common/constants';
import {
  infoServiceClient,
  PermissionPoint,
  Target,
} from '@http_idl/infoService';
import { isNil } from 'lodash-es';
import { useVisibleState } from '@hooks/useVisibleState';

export const useInfoDetailStateManage = () => {
  const history = useHistory();
  const { id } = queryString.parse(history.location.search) || ({} as any);
  const { staff } = useStores();
  const { staffInfoType } = staff || {};
  const accessPartyId = shared.getAccessPartyId();
  const topRef = useRef(null);
  const [topHeight, setTopHeight] = useState<number>(0);
  const {
    visible: divertVisible,
    onShow: onDivertShow,
    onCancel: onDivertCancel,
  } = useVisibleState({});
  const {
    formSchema,
    viewSchema,
    initValue,
    formStatus,
    formTitle,
    formId,
    initStarlingMap,
    filedMap,

    loading,
    submitLoading,
    saveLoading,
    isView,
    isFormChange,

    formApiRef,
    viewFormApiRef,
    SubmitValueRef,
    starlingMapRef,

    handleSubmit,
    handleSave,
    handleOnBack,
    handleOnValueChange,
    handleOnBeforeClickTranslation,
    handleOnClickChangeLang,
    handleStarlingChange,
  } = useWindowFormState({
    target: Target.Info,
    id,
    permission: staffInfoType,
    onBack: () => history.push('/info/list'),
  });

  useCspI18n({
    loading,
    fieldMap: filedMap,
    initStarlingMap,
    target: Target.Info,
    handleStarlingChange,
  });

  const [gqlLoading, setGqlLoading] = useState<boolean>(false);

  // 点击跳转GQL
  const handleOnGoGQL = useCallback(async () => {
    try {
      if (gqlLoading) {
        return;
      }
      setGqlLoading(true);
      const api = isView ? viewFormApiRef : formApiRef;
      const newData = api?.current?.getValues();
      const result = await infoServiceClient.GetGqlLink({
        newData: JSON.stringify(newData),
      });
      if (result?.gqlLink) {
        window.open(result.gqlLink);
      }
    } finally {
      setGqlLoading(false);
    }
  }, [isView, gqlLoading]);

  /**
   * 流转成功回调函数，如果是新建，则直接url导入id，如果是编辑，则直接隐藏弹窗
   */
  const handleDivertSuccess = useCallback(
    (saveInfoId: string): void => {
      if (formId !== saveInfoId) {
        history.replace(`/info/detail?id=${saveInfoId}`);
      }
      onDivertCancel();
    },
    [history, formId],
  );

  useEffect(() => {
    if (!accessPartyId) {
      return;
    }
    const val = localStorage.getItem(ACCESS_INFO_PARTY_KEY);
    // 如果在编辑页切换接入方跳转到列表页
    if (val && accessPartyId !== val) {
      history.replace('/info/list');
    }
    localStorage.setItem(ACCESS_INFO_PARTY_KEY, accessPartyId);
  }, [accessPartyId, history]);

  useEffect(() => {
    setTopHeight(topRef?.current?.offsetHeight || 0);
  }, [loading, topRef?.current?.offsetHeight]);

  return {
    FormBaseParams: {
      loading,
      topRef,
      gqlLoading,
      formTitle,
      formStatus,
      isChange: isFormChange,
      noPermission: staffInfoType === PermissionPoint.Operate,
      onBack: handleOnBack,
      onGoGQL: handleOnGoGQL,
    },
    // 表单主体状态数据
    FormRenderParams: {
      formSchema, // 默认编辑表单Schema
      viewSchema, // 国际化预览状态下的Schema
      formApiRef, // 默认编辑表单API
      viewFormApiRef, // 国际化预览表单API
      isView, // 是否是预览态，是的话使用预览表单
      initValue, // 初始化默认值
      saveLoading, // 保存loading
      submitLoading, // 发布loading，发布时会有文字提示
      style: { height: `calc(100vh - 170px - ${topHeight}px)` },
      onValueChange: handleOnValueChange,
    },
    // 表单底部操作按钮
    FormFooterParams: {
      target: Target.Info,
      submitLoading,
      saveLoading,
      filedMap,
      status: formStatus,
      isEdit: !isNil(id),
      onBeforeTranslation: handleOnBeforeClickTranslation,
      onClickLang: handleOnClickChangeLang,
      onSubmit: handleSubmit,
      onSave: handleSave,
      onCancel: handleOnBack,
      onDivert: onDivertShow,
    },
    // 流转弹窗数据
    TransferModalParams: {
      infoId: formId,
      permission: staffInfoType,
      isView,
      formApiRef,
      viewFormApiRef,
      SubmitValueRef,
      starlingMapRef,
      visible: divertVisible,
      onCancel: onDivertCancel,
      onDivertSuccess: handleDivertSuccess,
    },
  };
};
