import { isEmpty } from 'lodash-es';
import SpinLoading from '@components/CustomerFormRender/SpinLoading';
import BackNav from '@components/WindowForm/BackNav';
import FormTitle from '@components/WindowForm/FormTitle';
import FormFooter from '@components/WindowForm/Footer';
import FormRender from '@components/WindowForm/FormRender';
import { observer } from 'mobx-react';
import {
  COMMON_FE_TEXT_MAP,
  INFO_FE_TEXT_MAP,
} from '@common/constants/I18nTextMap';
import TransferModal from '@components/TransferModal';
import NoAgentError from '@components/global/noAgentError';
import SchemaError from '@components/global/SchemaError';
import { Button } from '@hi-design/ui';
import { useInfoDetailStateManage } from './useStateManage';
import Style from './index.module.scss';
import { Prompt } from '@edenx/runtime/router-v5';

const InfoDetail = () => {
  const {
    FormRenderParams,
    TransferModalParams,
    FormFooterParams,
    FormBaseParams,
  } = useInfoDetailStateManage();

  const {
    topRef,
    loading,
    gqlLoading,
    formTitle,
    formStatus,
    noPermission,
    isChange,
    onBack,
    onGoGQL,
  } = FormBaseParams || {};

  if (loading) {
    return (
      <div className={Style.LoadingBox}>
        <SpinLoading loading={loading} />
      </div>
    );
  }
  if (noPermission) {
    return <NoAgentError />;
  }
  if (isEmpty(FormRenderParams?.formSchema)) {
    return <SchemaError />;
  }

  return (
    <div className={Style.InfoContent}>
      <BackNav onBack={onBack} title={INFO_FE_TEXT_MAP.Back_Tips} />
      <div ref={topRef} className={Style.InfoContentTop}>
        <FormTitle title={formTitle} status={formStatus} />
        <Button
          theme="solid"
          type="primary"
          className={Style.InfoContentTopButton}
          onClick={onGoGQL}
          loading={gqlLoading}
        >
          GQL Debug
        </Button>
      </div>
      <FormRender {...FormRenderParams} />
      <FormFooter
        className={Style.WindowInfoFormFooter}
        {...FormFooterParams}
      />
      <TransferModal {...TransferModalParams} />
      <Prompt
        when={isChange}
        message={() => COMMON_FE_TEXT_MAP.Prompt_Message(formTitle)}
      />
    </div>
  );
};

export default observer(InfoDetail);
