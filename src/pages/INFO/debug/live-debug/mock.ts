// Mock data for the info live debug hook

// Interface for the API response of GetInfoInitTestData
export interface InfoInput {
  field: string;
  fieldName: string;
  fieldDesc: string;
  fieldTestVal: string;
  fieldOfflineVal: string;
  fieldType: string;
  fieldIsFixed: boolean;
}

// Mock data for the initial test data
export const mockInfoFields: InfoInput[] = [
  {
    field: 'userId',
    fieldName: 'User ID',
    fieldDesc: 'Unique identifier for the user',
    fieldTestVal: '12345',
    fieldOfflineVal: '',
    fieldType: 'string',
    fieldIsFixed: false,
  },
  {
    field: 'orderId',
    fieldName: 'Order ID',
    fieldDesc: 'Unique identifier for the order',
    fieldTestVal: '',
    fieldOfflineVal: 'ORD-9876',
    fieldType: 'string',
    fieldIsFixed: false,
  },
  {
    field: 'timestamp',
    fieldName: 'Timestamp',
    fieldDesc: 'Time of the request',
    fieldTestVal: '1620000000000',
    fieldOfflineVal: '1630000000000',
    fieldType: 'number',
    fieldIsFixed: true,
  },
  {
    field: 'region',
    fieldName: 'Region',
    fieldDesc: 'Geographical region of the user',
    fieldTestVal: 'US',
    fieldOfflineVal: 'EU',
    fieldType: 'string',
    fieldIsFixed: false,
  },
  {
    field: 'isPremium',
    fieldName: 'Is Premium',
    fieldDesc: 'Whether the user is a premium member',
    fieldTestVal: 'true',
    fieldOfflineVal: 'false',
    fieldType: 'boolean',
    fieldIsFixed: false,
  },
  {
    field: 'retryCount',
    fieldName: 'Retry Count',
    fieldDesc: 'Number of retry attempts',
    fieldTestVal: '2',
    fieldOfflineVal: '0',
    fieldType: 'number',
    fieldIsFixed: false,
  },
  {
    field: 'deviceType',
    fieldName: 'Device Type',
    fieldDesc: 'Type of device used by the user',
    fieldTestVal: 'mobile',
    fieldOfflineVal: 'desktop',
    fieldType: 'string',
    fieldIsFixed: false,
  },
  {
    field: 'apiVersion',
    fieldName: 'API Version',
    fieldDesc: 'Version of the API being used',
    fieldTestVal: 'v2.1.3',
    fieldOfflineVal: 'v1.9.8',
    fieldType: 'string',
    fieldIsFixed: true,
  },
  {
    field: 'userScore',
    fieldName: 'User Score',
    fieldDesc: 'Score calculated for the user',
    fieldTestVal: '85',
    fieldOfflineVal: '90',
    fieldType: 'number',
    fieldIsFixed: false,
  },
  {
    field: 'referrer',
    fieldName: 'Referrer',
    fieldDesc: 'The source that referred the user',
    fieldTestVal: 'campaign-xyz',
    fieldOfflineVal: 'organic',
    fieldType: 'string',
    fieldIsFixed: false,
  },
  {
    field: 'currency',
    fieldName: 'Currency',
    fieldDesc: 'Transaction currency',
    fieldTestVal: 'USD',
    fieldOfflineVal: 'EUR',
    fieldType: 'string',
    fieldIsFixed: true,
  },
  {
    field: 'shippingMethod',
    fieldName: 'Shipping Method',
    fieldDesc: 'Method chosen for shipping',
    fieldTestVal: 'express',
    fieldOfflineVal: 'standard',
    fieldType: 'string',
    fieldIsFixed: false,
  },
  {
    field: 'discountApplied',
    fieldName: 'Discount Applied',
    fieldDesc: 'Whether a discount was applied',
    fieldTestVal: 'false',
    fieldOfflineVal: 'true',
    fieldType: 'boolean',
    fieldIsFixed: false,
  },
  {
    field: 'userAgent',
    fieldName: 'User Agent',
    fieldDesc: 'User agent string from the request',
    fieldTestVal: 'Mozilla/5.0',
    fieldOfflineVal: 'Chrome/110.0',
    fieldType: 'string',
    fieldIsFixed: false,
  },
  {
    field: 'sessionId',
    fieldName: 'Session ID',
    fieldDesc: 'Unique identifier for the session',
    fieldTestVal: 'sess-abc-123',
    fieldOfflineVal: 'sess-def-456',
    fieldType: 'string',
    fieldIsFixed: false,
  },
];

// Mock response for the get request data from log ID API
export const mockLogIdResponse = {
  infoTestData: {
    userId: '54321',
    orderId: 'ORD-1234',
    timestamp: '1640000000000',
  } satisfies Record<string, string>,
  infoTestDataSuccess: true,
  infoTestDataTip: '',
};

// Mock error response for the get request data from log ID API
export const mockLogIdErrorResponse = {
  infoTestData: {} satisfies Record<string, string>,
  infoTestDataSuccess: false,
  infoTestDataTip: 'Failed to get data from log ID: Log ID not found',
};

export const mockExecResult = {
  queryResults: [
    {
      infoKey: '$.data.user.getProfile.age',
      infoValue: '30',
      valueSource: 1,
    },
    {
      infoKey: '$.data.user.getProfile.gender',
      infoValue: 'male',
      valueSource: 1,
    },
    {
      infoKey: '$.data.get_refund_order_knowledge.refund_method_name',
      infoValue: 'Return and Refund',
      valueSource: 1,
    },
    {
      infoKey: '$.data.MGetFulfillmentSLA.deliver_info.is_delivery_over_time',
      infoValue: 'false',
      valueSource: 2,
    },
  ],
  errors: [],
};
