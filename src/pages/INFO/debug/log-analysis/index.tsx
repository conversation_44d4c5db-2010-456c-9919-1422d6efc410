import { LogAnalysisForm } from '@/components/log-analysis/log-form';
import useInfoLogAnalysis from './use-info-log-analysis';
import styles from './index.module.scss';
import { LogOverview } from '@/components/log-analysis/log-overview';
import { Banner, Typography } from '@hi-design/ui';
import { LogRequestData } from '@/components/log-analysis/log-request-data';
import { LogRecord } from '@/components/log-analysis/log-record';
import { INFO_DEBUG_TEXT_MAP } from '@/common/constants/I18nTextMap';
import { NoData } from '@/components/log-analysis/no-data';

export const LogAnalysis = () => {
  const {
    isSearching,
    formValues,
    hasSearchReturned,
    isSearchSuccess,
    logData,
    argosLink,
    requestData,
    currentInfo,
    handleSearchValueChange,
    handleStopSearch,
    onSubmit,
  } = useInfoLogAnalysis();
  return (
    <div className={styles.logAnalysis}>
      <LogAnalysisForm
        isLoading={isSearching}
        hasSearchReturned={hasSearchReturned}
        isSearchSuccess={isSearchSuccess}
        argosLink={argosLink}
        initialValues={formValues}
        handleValueChange={handleSearchValueChange}
        handleStopSearch={handleStopSearch}
        onSubmit={onSubmit}
      />
      {hasSearchReturned ? (
        isSearchSuccess ? (
          <div className={styles.logResult}>
            <section>
              <Typography.Title heading={6} className={styles.sectionHeader}>
                {INFO_DEBUG_TEXT_MAP.EXECUTION_OVERVIEW}
              </Typography.Title>

              <LogOverview
                callerPSM={formValues.psm}
                executionNodes={logData?.errorList}
              />
            </section>
            <section>
              <Typography.Title
                heading={6}
                className={styles.sectionHeader}
                style={{ marginBottom: 0 }}
              >
                {INFO_DEBUG_TEXT_MAP.REQUEST_DATA}
              </Typography.Title>
              <LogRequestData
                requestData={requestData}
                activeInfoKey={currentInfo?.infoKey}
              />
            </section>
            <section>
              <div className={styles.sectionHeader}>
                <Typography.Title heading={6} style={{ marginBottom: 4 }}>
                  {INFO_DEBUG_TEXT_MAP.EXECUTION_RECORD}
                </Typography.Title>
                <Typography.Text type="tertiary" size="small">
                  {INFO_DEBUG_TEXT_MAP.EXECUTION_RECORD_DESC}
                </Typography.Text>
              </div>
              {logData?.errorList ? (
                <LogRecord errorList={logData?.errorList} />
              ) : (
                <NoData />
              )}
            </section>
          </div>
        ) : (
          <Banner
            className={styles.banner}
            fullMode
            hideClose
            type="danger"
            description={INFO_DEBUG_TEXT_MAP.LOG_NOT_FOUND}
          />
        )
      ) : null}
    </div>
  );
};
