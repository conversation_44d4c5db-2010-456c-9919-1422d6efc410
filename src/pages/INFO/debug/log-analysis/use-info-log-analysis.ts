import { useCallback, useMemo, useRef, useState } from 'react';
import { LOG_REGION_OPTIONS } from '@/common/constants/info';
import { LogAnalysisForm } from '@/components/log-analysis/log-form';
import { useHistory } from '@edenx/plugin-router-v5/runtime';
import queryString from 'query-string';
import { LogRequest } from '@/components/log-analysis/log-request-data';
import { format } from 'date-fns';
import { InfoDetail } from '@/types/info-debug';
import { useStores } from '@/stores';
import { infoServiceRpcService } from '@/common/http/infoServiceRPCService';
import { info_service } from '@/bam-auto-generate/infoServiceRPC';
import { Toast } from '@hi-design/ui';
import { ErrorResponse } from '@/common/http';

export interface UseInfoLogAnalysisResult {
  isSearching: boolean;
  hasSearchReturned: boolean;
  isSearchSuccess: boolean;
  formValues: LogAnalysisForm;
  logData: info_service.CheckDMLogMileStoneResponse | null;
  requestData: LogRequest[];
  argosLink?: string;
  currentInfo: InfoDetail | null;
  handleSearchValueChange: (values: LogAnalysisForm) => void;
  handleStopSearch: () => void;
  onSubmit: () => void;
}

const useInfoLogAnalysis = () //   props: IUseInfoDebugProps,
: UseInfoLogAnalysisResult => {
  const history = useHistory();
  const query = queryString.parse(history.location.search) || {};
  const { logID, region, psm } = query;
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearchReturned, setHasSearchReturned] = useState(false);
  const [isSearchSuccess, setIsSearchSuccess] = useState(false);
  const [logData, setLogData] =
    useState<info_service.CheckDMLogMileStoneResponse | null>(null);
  const abortController = useRef<AbortController>();
  const { info } = useStores();
  const { currentInfo } = info || {};

  const resetResult = () => {
    setLogData(null);
    setHasSearchReturned(false);
  };

  const handleSearchLog = useCallback(async () => {
    resetResult();
    if (abortController.current) {
      return;
    }
    const controller = new AbortController();
    abortController.current = controller;
    try {
      setIsSearching(true);

      const res = await infoServiceRpcService.CheckDMLogMileStone({
        logId: logID as string,
        dataCenter: region as string,
        upstreamPsm: psm as string,
      });

      setLogData(res);
      setHasSearchReturned(true);
      setIsSearchSuccess(res.logFindSuccess || false);
    } catch (error) {
      setHasSearchReturned(false);

      const err = error as ErrorResponse;
      Toast.error(
        `Failed to search log. ${err.statusMsg || ''} ${err.error || ''}`,
      );
      console.error(error);
    } finally {
      abortController.current = undefined;
      setIsSearching(false);
    }
  }, [logID, region, psm]);

  const requestData = useMemo(() => {
    if (!logData?.queryResults?.length) {
      return [];
    }

    const formatted = logData.queryResults
      ?.sort((a, b) => Number(a.requestId || 0) - Number(b.requestId || 0))
      ?.map((item, idx) => ({
        key: String(item.requestId) || String(idx),
        tabDesc: item.timeStamp
          ? format(
              new Date(Number(item.timeStamp) / 1000),
              'yyyy-MM-dd HH:mm:ss.SSS',
            )
          : '-',
        requestParams: Object.entries(item.requestParams || {})
          .map(([key, value]) => ({
            title: key,
            value: String(value),
          }))
          .sort((a, b) => a.title.localeCompare(b.title)),
        infoKeyResults: item.infoKeyResults || [],
        infoKeyDiff:
          item.requestInfokeys?.filter(
            out => !item.infoKeyResults?.some(inItem => inItem.InfoKey === out),
          ) || [],
      }));

    return formatted;
  }, [logData]);

  return {
    isSearching,
    hasSearchReturned,
    isSearchSuccess,
    formValues: {
      logID: logID as string,
      region: LOG_REGION_OPTIONS.filter(item => item?.value === 'ROW')?.[0]
        ?.value,
      psm: psm as string,
    },
    logData,
    requestData,
    argosLink: logData?.logUrl || '',
    currentInfo,
    handleSearchValueChange: (values: LogAnalysisForm) => {
      const stringfied = queryString.stringify({
        ...query,
        ...values,
      });
      history.replace(`/info/debug?${stringfied}`);
    },
    handleStopSearch: () => {
      abortController.current?.abort();
      setIsSearching(false);
    },
    onSubmit: handleSearchLog,
  };
};
export default useInfoLogAnalysis;
