export default {
  executionProgress: {
    '0': {
      status: 1,
      errorDetails: [
        'Failed to establish SSL handshake with upstream service after 5 seconds.',
        'Connection aborted by remote host after multiple retry attempts.',
      ],
      logs: [
        {
          logMsg: 'Starting connection to upstream service',
          pod: 'upstream-pod-1',
          ip: '************',
        },
        {
          logMsg: 'SSL handshake timed out',
          pod: 'upstream-pod-2',
          ip: '************',
        },
        {
          logMsg: 'Retrying request to upstream endpoint',
          pod: 'upstream-pod-1',
          ip: '************',
        },
        {
          logMsg: 'Upstream connection aborted by peer',
          pod: 'upstream-pod-3',
          ip: '************',
        },
      ],
    },
    '1': {
      status: 2,
      errorDetails: [
        'field_name=oec_fulfillment_center_MGetFulfillmentSLA commonRpcFunc failedrpcService failed.req=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]]CommonRpcServiceInner RpcCall failed,statusCode=21001008 is not 0,clientType=Kitex, psm=oec.fulfillment.center,method=MGetFulfillmentSLA,requestMap=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]],data=&{data:map[BaseResp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}] base_resp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}]] err:<nil>},statusMessage=prepare data err: {bizErr|21001008|not found error|fulfill order not found},code=21001008,err=<nil>',
        'field_name=oec_fulfillment_center_MGetFulfillmentSLA commonRpcFunc failedrpcService failed.req=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]]CommonRpcServiceInner RpcCall failed,statusCode=21001008 is not 0,clientType=Kitex, psm=oec.fulfillment.center,method=MGetFulfillmentSLA,requestMap=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]],data=&{data:map[BaseResp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}] base_resp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}]] err:<nil>},statusMessage=prepare data err: {bizErr|21001008|not found error|fulfill order not found},code=21001008,err=<nil>',
        'field_name=oec_fulfillment_center_MGetFulfillmentSLA commonRpcFunc failedrpcService failed.req=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]]CommonRpcServiceInner RpcCall failed,statusCode=21001008 is not 0,clientType=Kitex, psm=oec.fulfillment.center,method=MGetFulfillmentSLA,requestMap=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]],data=&{data:map[BaseResp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}] base_resp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}]] err:<nil>},statusMessage=prepare data err: {bizErr|21001008|not found error|fulfill order not found},code=21001008,err=<nil>',
        'field_name=oec_fulfillment_center_MGetFulfillmentSLA commonRpcFunc failedrpcService failed.req=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]]CommonRpcServiceInner RpcCall failed,statusCode=21001008 is not 0,clientType=Kitex, psm=oec.fulfillment.center,method=MGetFulfillmentSLA,requestMap=map[main_order_ids:[xxxx] order_line_ids:[] package_ids:[]],data=&{data:map[BaseResp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}] base_resp:map[StatusCode:21001008 StatusMessage:prepare data err: {bizErr|21001008|not found error|fulfill order not found}]] err:<nil>},statusMessage=prepare data err: {bizErr|21001008|not found error|fulfill order not found},code=21001008,err=<nil>',
      ],
      logs: [
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: ee58cd81, Span ID: fedc0ab1.',
          pod: 'jgti-yxfip-oiqs-bjsnhnz-bnuphaq',
          ip: '***************',
          spanId: 'd0b35cf94fc7bb4548ac3ed27abaa41e',
          timestamp: 1747807331244,
          idc: 'AWS',
          psm: 'ies.scheduler.jobs',
          logId: '93fbd7db91afffb6af7061f08f239097',
          location: 'auth_handler.go',
          level: 'WARN',
          ifPpe: false,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: cc6abe1d, Span ID: b31764cb.',
          pod: 'neszvjg-blkdz-licx-yhpedcp-ejjx',
          ip: '**************',
          spanId: 'aefdc26fb00d1cf18e91bc0d77dd7fc6',
          timestamp: 1747809119244,
          idc: 'AWS',
          psm: 'ies.cache.layer',
          logId: '3ebbfa25edc26ff0dd1ef1d2f1cc8eb9',
          location: 'data_parser.go',
          level: 'INFO',
          ifPpe: true,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: 869c40f2, Span ID: e5ab3aef.',
          pod: 'axwlhyiz-zhvdezzm-nvrlbgnd-muyukitr-gklw',
          ip: '**************',
          spanId: 'd7cff4fb0e8fa1ebfda4fe21f1accaa3',
          timestamp: 1747805958245,
          idc: 'GCP',
          psm: 'ies.auth.service',
          logId: '238a6adb9eb0ca0c52dc4aaddd4b4ab3',
          location: 'kafka_consumer.go',
          level: 'ERROR',
          ifPpe: true,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: 9f741a32, Span ID: 1d025cba.',
          pod: 'gocefopf-pctbic-oazhplbx-uzyke-fzln',
          ip: '*************',
          spanId: '7e46d35c2d20d3ea4feded381bc6540c',
          timestamp: 1747805244245,
          idc: 'AWS',
          psm: 'ies.cache.layer',
          logId: 'fcc98b1bbb3a82a1e4696a1ae6c3fbf3',
          location: 'kafka_consumer.go',
          level: 'DEBUG',
          ifPpe: false,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: 3b8e9c0a, Span ID: 246aa706.',
          pod: 'wexacju-vqcmcsl-nkcci-cdzqvff-ekpgsnn',
          ip: '*************',
          spanId: 'c5eede35fcee1d5fd7873e1acfe80965',
          timestamp: 1747806219245,
          idc: 'IDC1',
          psm: 'ies.scheduler.jobs',
          logId: 'fdb2fb2ad763b36e7ddd4fddca730da1',
          location: 'kafka_consumer.go',
          level: 'ERROR',
          ifPpe: true,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: 29ae439e, Span ID: 8b14acda.',
          pod: 'blzpnwb-gfqukwo-ajrtxjzk-plfmre-nqftcu',
          ip: '************',
          spanId: 'e0a78cd3bcb6d78e403655a061cf2d17',
          timestamp: 1747807960245,
          idc: 'IDC2',
          psm: 'ies.db.access',
          logId: '3a3b0f368f0f77d7b4c2ae8456ef0d61',
          location: 'mysql_connector.go',
          level: 'INFO',
          ifPpe: false,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: 1f7c391d, Span ID: 8be8f6b4.',
          pod: 'segguxzq-zvfcprwi-jffkhn-cqzxegou-xzvoryr',
          ip: '*************',
          spanId: '1c8932be4e3a7402e2c8b9736a3a78e7',
          timestamp: 1747806512245,
          idc: 'IDC1',
          psm: 'ies.db.access',
          logId: 'd93f9a60fa5c1c2cf735c1cb5b26e2e6',
          location: 'mysql_connector.go',
          level: 'WARN',
          ifPpe: false,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: 0a5fd1d2, Span ID: 3e1b9910.',
          pod: 'duqowmbj-kbtqzv-jlediygi-fzrapbwg-jcmzpak',
          ip: '*************',
          spanId: '26320bc53dc8f235fc926e41783f0ff9',
          timestamp: 1747805403245,
          idc: 'IDC3',
          psm: 'ies.gateway.core',
          logId: 'bd7b869188bc1fffa4a36aa452cda9d0',
          location: 'info_service.go',
          level: 'INFO',
          ifPpe: false,
        },
        {
          logMsg:
            'This is a detailed system log message indicating that a specific condition occurred in the application flow, which might include HTTP errors, internal state changes, retries, parsing issues, or other debug-level events. Full context and traceable IDs included. Log ID: 1dd429ea, Span ID: db283c4d.',
          pod: 'lzglvpkf-vhzwxth-lkbpyr-jqiguwr-msahokg',
          ip: '***********',
          spanId: '66cc92dc9f219a5e21e47928dcf2d3de',
          timestamp: 1747808969245,
          idc: 'GCP',
          psm: 'ies.gateway.core',
          logId: '2893db62eb7a7ff0f3260896e403b4cb',
          location: 'info_service.go',
          level: 'ERROR',
          ifPpe: true,
        },
      ],
    },
    '2': {
      status: 2,
      errorDetails: [
        'Error executing DM script: NullPointerException at line 45.',
        'Script aborted before milestone check could complete.',
      ],
      logs: [
        {
          logMsg: 'Loading DM script milestone module',
          pod: 'dmscript-pod-1',
          ip: '************',
        },
        {
          logMsg: 'Null reference encountered in milestoneData object',
          pod: 'dmscript-pod-1',
          ip: '************',
        },
        {
          logMsg: 'Terminating script due to unhandled exception',
          pod: 'dmscript-pod-2',
          ip: '************',
        },
      ],
    },
    '3': {
      status: 0,
      errorDetails: [
        'Downstream RPC endpoint unreachable: dial tcp ************:9090 i/o timeout.',
        'No fallback endpoint configured, aborting request.',
      ],
      logs: [
        {
          logMsg: 'Initiating RPC call to downstream service',
          pod: 'downstreamrpc-pod-1',
          ip: '************',
        },
        {
          logMsg: 'Dial timeout after 3 seconds',
          pod: 'downstreamrpc-pod-1',
          ip: '************',
        },
        {
          logMsg: 'RPC client closing connection',
          pod: 'downstreamrpc-pod-2',
          ip: '************',
        },
      ],
    },
  },
  queryResults: [
    {
      requestId: '20250516123045123',
      timestamp: '1747784754123',
      requestParams: {
        mainOrderId: '578836133454383052',
        region: 'PH',
        userId: '5678901234567890',
        sessionId: 'sess-abcdef123456',
        deviceType: 'web',
        timestamp: '2025-05-20T00:00:00Z',
        superlong: '567890123456789056789012345678905678901234567890',
        dataCenter: 'GCP',
        upstreamPsm: 'psm-frontend',
        page: 1,
        pageSize: 100,
        sortBy: 'orderDate',
        sortOrder: 'asc',
      },
      requestInfokeys: [
        '$.data.MGetFulfillmentSLA.deliver_info.is_delivery_over_time',
        '$.data.user.getProfile.age',
        '$.data.user.getProfile.gender',
        '$.data.pack_details_info.safe_place',
      ],
      infoKeyResults: [
        {
          infoKey:
            '$.data.MGetFulfillmentSLA.deliver_info.is_delivery_over_time',
          infoValue: '',
          valueSource: 3,
        },
        {
          infoKey: '$.data.user.getProfile.age',
          infoValue: '123',
          valueSource: 2,
        },
        {
          infoKey: '$.data.order.getMainOrderStatus.status',
          infoValue: 'DELIVERED',
          valueSource: 1,
        },
        {
          infoKey: '$.data.logistics.getTrackingInfo.last_updated_time',
          infoValue: '2025-05-20T14:33:00Z',
          valueSource: 3,
        },
        {
          infoKey: '$.data.product.getProductDetail.name',
          infoValue:
            '[{"titleDescription":"","isFinishStep":false,"status":"finish","extContents":[{"title":"Seller Registeration","titleTag":{"tagLabel":"","tagValue":""},"descriptions":[],"status":"finish"},{"titleTag":{"tagLabel":"","tagValue":""},"title":"DDQ Validate","descriptions":[],"status":"finish"}],"title":"Registration","subTitle":"Aug 21, 12:22","sortTime":1724268156},{"sortTime":1724268161,"titleDescription":"","title":"System Moderation","subTitle":"Aug 21, 12:22","isFinishStep":false,"status":"finish","extContents":[{"title":"Velocity check","descriptions":[],"titleTag":{"tagLabel":"","tagValue":""},"status":"finish"},{"title":"Pre KYC/KYB","titleTag":{"tagLabel":"","tagValue":""},"descriptions":[],"status":"finish"},{"titleTag":{"tagValue":"","tagLabel":""},"descriptions":[],"status":"finish","title":"KYC/B System Moderation"},{"title":"Fake ID System Moderation","descriptions":[],"titleTag":{"tagValue":"","tagLabel":""},"status":"wait"}]},{"title":"Human Moderation","titleDescription":"","status":"finish","extContents":[{"title":"KYC/B Human Moderation","titleTag":{"tagLabel":"","tagValue":""},"descriptions":[],"status":"finish"}],"sortTime":1724268187,"subTitle":"Aug 21, 12:23","isFinishStep":false},{"status":"finish","extContents":[{"title":"EA Check","descriptions":[],"status":"","titleTag":{"tagLabel":"","tagValue":""}},{"title":"PIPO Check","titleTag":{"tagLabel":"","tagValue":""},"descriptions":[],"status":"finish"}],"isFinishStep":false,"sortTime":1724268213,"subTitle":"Aug 21, 12:23","titleDescription":"","title":"EA\\u0026PIPO Check"},{"title":"UBO Check","titleDescription":"272d 1.3h","extContents":[{"title":"UBO Check","titleTag":{"tagLabel":"Rejected","tagValue":"2"},"descriptions":["We appreciate your interest in becoming a TikTok Shop seller. Unfortunately, we are unable to onboard businesses whose ownership are based in your region at the moment. We are continuously working on expanding our reach, so please stay tuned for any updates."],"status":"error"}],"sortTime":1724278421,"subTitle":"Aug 21, 15:13","isFinishStep":false,"status":"error"},{"subTitle":"","titleDescription":"","isFinishStep":true,"status":"wait","extContents":[{"title":"Tax Check","descriptions":[],"status":"wait","titleTag":{"tagLabel":"","tagValue":""}},{"descriptions":[],"status":"wait","title":"Inform Act","titleTag":{"tagLabel":"","tagValue":""}}],"title":"Post Onboarding","sortTime":2600000000}]',
          valueSource: 2,
        },
        {
          infoKey: '$.data.payment.getTransactionInfo.amount',
          infoValue: '49.99',
          valueSource: 1,
        },
        {
          infoKey: '$.data.customer.getSupportTicket.status',
          infoValue: 'RESOLVED',
          valueSource: 2,
        },
        {
          infoKey: '$.data.inventory.getStockLevel.quantity',
          infoValue: '150',
          valueSource: 3,
        },
        {
          infoKey: '$.data.review.getLatestReview.rating',
          infoValue: '4.8',
          valueSource: 2,
        },
        {
          infoKey: '$.data.user.getPreferences.language',
          infoValue: 'en-US',
          valueSource: 1,
        },
        {
          infoKey: '$.data.order.getOrderNotes.notes',
          infoValue: 'Urgent delivery requested',
          valueSource: 2,
        },
        {
          infoKey: '$.data.refund.getRefundStatus.is_approved',
          infoValue: 'true',
          valueSource: 3,
        },
      ],
    },
    {
      requestId: '20250516123100999',
      timestamp: '1747784704456',
      requestParams: {
        orderId: '4035796563630262220',
      },
      requestInfokeys: [
        '$.data.order.calculateTax',
        '$.data.order.mainOrderId',
      ],
      infoKeyResults: [
        {
          infoKey: '$.data.order.calculateTax',
          infoValue: '',
          valueSource: 3,
        },
      ],
    },
  ],
  logFindSuccess: true,
  BaseResp: {
    code: 1,
    message: 'Overall execution failed',
  },
};
