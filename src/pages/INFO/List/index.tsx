import { FC, useEffect } from 'react';
import styles from './index.module.scss';
import { shared } from '@ies/united-sdk-i18n';
import { INFO_FE_TEXT_MAP } from '@common/constants/I18nTextMap';
import { ACCESS_API_PARTY_KEY } from '@common/constants';
import { Target } from '@http_idl/infoService';
import ListContent from '@components/ListContent';
import PageHeader from '@/components/page-header';

const InfoManagement: FC = () => {
  useEffect(() => {
    const accessPartyId = shared.getAccessPartyId();
    localStorage.setItem(ACCESS_API_PARTY_KEY, accessPartyId!);
  }, [shared.getAccessPartyId()]);
  return (
    <div className={styles.page}>
      <PageHeader
        title={INFO_FE_TEXT_MAP.Title}
        description={INFO_FE_TEXT_MAP.Desc}
      />
      <div className={styles.content}>
        <ListContent target={Target.Info} />
      </div>
    </div>
  );
};

export default InfoManagement;
