html,
body {
  padding: 0;
  margin: 0;
  font-family:
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
}

p {
  margin: 0;
}

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box;
}

.container-box {
  min-height: 100vh;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 10px;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  display: flex;
  margin: 4rem 0 4rem;
  align-items: center;
  font-size: 4rem;
  font-weight: 600;
}

.logo {
  width: 3.6rem;
  margin: 0 1.6rem;
}

.name {
  background: -webkit-linear-gradient(315deg, #788ec9 25%, #5978c0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.description {
  text-align: center;
  line-height: 1.5;
  font-size: 1.3rem;
  color: #1b3a42;
  margin-bottom: 5rem;
}

.code {
  background: #fafafa;
  border-radius: 12px;
  padding: 0.6rem 0.9rem;
  font-size: 1.05rem;
  font-family:
    Menlo,
    Monaco,
    Lucida Console,
    Liberation Mono,
    DejaVu Sans Mono,
    Bitstream Vera Sans Mono,
    Courier New,
    monospace;
}

.container-box .grid {
  display: grid;
  grid-template-columns: repeat(3, 33.33%);
  grid-row-gap: 20px;
  grid-column-gap: 20px;
  width: 1100px;
  margin-top: 3rem;
  align-items: center;
  justify-content: center;
}

.card {
  background: #f9f9f9;
  border: 1px solid transparent;
  transition: all.3s;
  padding: 2rem;
  border-radius: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: inherit;
  text-decoration: none;
  transition: 0.15s ease;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon-text {
  text-align: center;
  height: 3rem;
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.card h2 {
  align-items: center;
  font-size: inherit;
  font-weight: 700;
  margin: 0;
  padding: 0;
  text-align: center;
}

.card p {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  padding-top: 0.5rem;
  color: rgba(60, 60, 60, 0.66);
}
