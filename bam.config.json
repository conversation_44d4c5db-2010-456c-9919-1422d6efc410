{"$schema": "https://sf-unpkg-src.bytedance.net/@byted-arch-fe/bam-code-generator@1.17.4/config-schema.json", "outDir": "./src/bam-auto-generate", "services": {"infoServiceRPC": {"psm": "ies.kefu.info_service@1.0.110", "parse": {"useRpc": true}, "generate": {"int64": "string"}}, "dataMarket": {"psm": "ies.kefu.datamarket@master", "parse": {"useRpc": true}}}, "log": {"infoServiceRPC": {"psm": "ies.kefu.info_service", "version": "1.0.115", "branch": "master", "apis": ["BatchGetInfo", "GetAction", "GetQueryByInfo", "GetQueryByAction", "GetInfo", "SearchInfo", "GetInfoConfigCheck", "GetApi", "UpdateOrAddData", "GetInsertOrUpdateSchema", "GetApiDataList", "GetOperateRecordList", "GetInfoSelectPageScheme", "GetApiSelectPageScheme", "DisableOrEnableInfoOrApi", "GetInfoDataList", "GetInfoInitParams", "SearchTransferUser", "DeleteInfo", "SaveInfo", "TransferInfo", "DeleteApi", "SaveApi", "GetGraphQLDebugLink", "GetGqlLink", "GetInfoTestData", "GetInfoTestResult", "GetFeatureBusinessLineAndScene", "GetInputFormatList", "QueryApiApplicationsAndAccessParty", "GetConfig", "QueryApiPsmPrefixList", "QueryMethodListByPsm", "GetUpdateData", "QueryInfoKeyList", "QueryApiAndInfoKeyListWithRepeatOrWrong", "GetVariablesByInfoKeys", "GetAllDataForDatamarket", "GetInfoReferenceDetail", "<PERSON><PERSON><PERSON><PERSON>", "GetInfoPage", "CheckDMLogMileStone", "GetInfoTestDataFromLogID", "GetInfoInitTestData", "BatchUpdateInfo"]}, "dataMarket": {"psm": "ies.kefu.datamarket", "version": "1.0.93", "branch": "master", "apis": ["Query", "WebQuery", "GetQueryList", "GetQueryHistoryList", "Update<PERSON><PERSON><PERSON>", "GetQueryInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "En<PERSON><PERSON><PERSON><PERSON>", "QueryByInfoKeys", "QueryByInfoKeysV2", "CheckInfoKeysIsValid", "GenQueryByInfoKeys", "GetQueryResolverMap", "ScenarioDataRewrite", "QueryByQueryID", "QueryByApaasQuery", "APaaSQueryExecute", "CommonAPaaSQuery", "CommonAPaaSAction", "APaaSActionTest", "BatchCommonAPaaSQuery", "APaaSWorkflowTest", "APaaSDynamicEnumExecute", "GenericRPCCall"]}}}